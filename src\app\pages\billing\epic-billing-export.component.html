<!-- Begin Page Content Starts -->
<div class="container-fluid">
    <!-- Content Row -->
    <div class="row">
        <!-- Area Chart -->
        <div class="col px-0">
            <div class="card shadow border-blue">
                <!-- Card Header - Dropdown -->
                <div class="card-header-new p-2" style="background: #0169ab;" [formGroup]="FilterForm">
                    <div class="align-items-center row mx-auto">
                        <div class="col-12 px-0">

                            <div class="row mt-1 mx-auto">
                                <div class="col-12 col-md-2 px-1 py-1">
                                    <select id="ddlFacility" class="form-control"
                                        formControlName="ddlFacility"
                                        (change)="getPhysiciansByFacility($any($event.target).value)"
                                        [ngClass]="{ 'is-invalid': submitted && f['ddlFacility'].errors}">
                                        <option value="">---Select Facility---</option>
                                        <option [value]="s.facilityName" *ngFor="let s of listOfFacilities">
                                            <span>{{s.facilityName}}</span>
                                        </option>
                                    </select>
                                    <div *ngIf="submitted && f['ddlFacility'].errors" class="invalid-feedback">
                                        <div *ngIf="f['ddlFacility'].errors['required']">Facility is required</div>
                                    </div>
                                </div>
                                <div class="col-12 col-md-2 px-1 py-1 multiselect-absolute">
                                    <ng-multiselect-dropdown class="report-multiselect form-control"
                                        [placeholder]="'--select groups--'" [data]="listOfGroups"
                                        [(ngModel)]="selectedListOfGroups" [settings]="mDdlGroupsSettings"
                                        formControlName="ddlGroups"
                                        (onSelect)="onGroupSelect($event)" (onDeSelect)="onGroupSelect($event)"
                                        (onSelectAll)="onGroupSelectAll($event,'S')"
                                        (onDeSelectAll)="onGroupSelectAll($event,'D')"
                                        [ngClass]="{ 'is-invalid': submitted && f['ddlGroups'].errors}">
                                    </ng-multiselect-dropdown>
                                </div>
                                <div class="col-12 col-md-2 px-1 py-1 multiselect-absolute">
                                    <ng-multiselect-dropdown class="report-multiselect form-control"
                                        [placeholder]="'--select physicians--'" [data]="listOfPhysicians"
                                        [(ngModel)]="selectedListOfPhysicians" [settings]="mDdlPhysicianSettings"
                                        formControlName="ddlPhysician"
                                        [ngClass]="{ 'is-invalid': submitted && f['ddlPhysician'].errors}">
                                    </ng-multiselect-dropdown>
                                </div>
                                <div class="col-12 col-md-2 px-1 py-1">
                                    <input id="encounterSeenDateFrom" type="date"
                                        class="form-control" formControlName="encounterSeenDateFrom"
                                        title="Mark as Billed From Date"
                                        [ngClass]="{ 'is-invalid': submitted && f['encounterSeenDateFrom'].errors}">
                                    <div *ngIf="submitted && f['encounterSeenDateFrom'].errors"
                                        class="invalid-feedback">
                                        <div *ngIf="f['encounterSeenDateFrom'].errors['required']">Mark as Billed From
                                            Date is required</div>
                                    </div>
                                </div>
                                <div class="col-12 col-md-2 px-1 py-1">
                                    <input id="encounterSeenDateTo" type="date" class="form-control"
                                        formControlName="encounterSeenDateTo" title="Mark as Billed To Date"
                                        [ngClass]="{ 'is-invalid': submitted && f['encounterSeenDateTo'].errors}">
                                    <div *ngIf="submitted && f['encounterSeenDateTo'].errors" class="invalid-feedback">
                                        <div *ngIf="f['encounterSeenDateTo'].errors['required']">Mark as Billed To Date
                                            is required</div>
                                    </div>
                                </div>
                                <div class="col-12 col-md-1 px-1 py-1"
                                [ngClass]="{'col-md-1': totalCountToDownload, 'col-md-2': !totalCountToDownload}">
                                    <button class="btn btn-outline-info px-2 btn-block" type="submit" (click)="filterReport('VIEW')">
                                        <i class="fa-filter fas font-size-13"></i>
                                        Filter
                                    </button>
                                </div>
                                <div class="col-12 col-md-1 px-1 py-1" *ngIf="totalCountToDownload>1">
                                    <img alt=' ' (keyup)="exportExcel()" (click)="exportExcel()" class="ml-2 shadow" [src]="img1"
                                        style="width:40px;cursor:pointer;" title="Export Excel">
                                </div>
                            </div>

                        </div>
                    </div>
                </div>

                <!-- Card Body -->
                <div class="card-body px-2 py-1">
                    <div class="row mx-0">
                        <div class="col-12 overflow-auto px-0">
                            <div class="table-responsive">
                                <table class="table table-striped epic-table-bordered my-auto">
                                    <thead>
                                        <tr style="font-size:14px;">
                                            <th class="text-center pb-0 cursor-pointer">Record Identifier</th>
                                            <th class="text-center pb-0 cursor-pointer">Transaction Type</th>
                                            <th class="text-center pb-0 cursor-pointer">Visit Number</th>
                                            <th class="text-center pb-0">Patient</th>
                                            <th class="text-center pb-0">Patient MRN</th>
                                            <th class="text-center pb-0 cursor-pointer">Patient MRN Type</th>
                                            <th class="text-center pb-0 cursor-pointer">Account Type</th>
                                            <th class="text-center pb-0">Provider</th>
                                            <th class="text-center pb-0 cursor-pointer">Billing Provider</th>
                                            <th class="text-center pb-0 cursor-pointer">Service Date</th>
                                            <th class="text-center pb-0 cursor-pointer">Place of Service</th>
                                            <th class="text-center pb-0 cursor-pointer">Place of Service Type</th>
                                            <th class="text-center pb-0 cursor-pointer"></th>
                                            <th class="text-center pb-0 cursor-pointer">Referral Source</th>
                                            <th class="text-center pb-0 cursor-pointer">Department</th>
                                            <th class="text-center pb-0 cursor-pointer">Quantity</th>
                                            <th class="text-center pb-0 cursor-pointer">Diagnosis</th>
                                            <th class="text-center pb-0 cursor-pointer">Diagnosis Code Set</th>
                                            <th class="text-center pb-0 cursor-pointer">DX Qualifier</th>
                                            <th class="text-center pb-0 cursor-pointer">DX Index</th>
                                            <th class="text-center pb-0 cursor-pointer">Procedure</th>
                                            <th class="text-center pb-0 cursor-pointer">Modifier</th>
                                            <th class="text-center pb-0 cursor-pointer">Admission Date</th>
                                            <th class="text-center pb-0 cursor-pointer">Discharge Date</th>
                                            <th class="text-center pb-0 cursor-pointer">Downloaded By</th>
                                            <th class="text-center pb-0 cursor-pointer">Downloaded Date</th>
                                            <th class="text-center pb-0 cursor-pointer">Batch No</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr class="{{item.is_downloaded? 'is_downloaded' : 'is_ready_to_download'}}"
                                         *ngFor="let item of listOfReports | paginate: { itemsPerPage: 10, currentPage: p,totalItems:totalCount}">
                                            <td>{{item.recordIdentifier}}</td>
                                            <td>{{item.transactionType}}</td>
                                            <td>{{item.visitNumber}}</td>
                                            <td>{{item.patient}}</td>
                                            <td>{{item.patientMRN}}</td>
                                            <td>{{item.patientMrnType}}</td>
                                            <td>{{item.accountType}}</td>
                                            <td>{{item.provider}}</td>
                                            <td>{{item.billingProvider}}</td>
                                            <td>{{item.serviceDate | date: 'MM/dd/yyyy'}}</td>
                                            <td>{{item.placeOfService}}</td>
                                            <td>{{item.placeOfServiceType}}</td>
                                            <td>{{item.noColumn3}}</td>
                                            <td>{{item.referralSource}}</td>
                                            <td>{{item.department}}</td>
                                            <td>{{item.quantity}}</td>
                                            <td>{{item.diagnosis}}</td>
                                            <td>{{item.diagnosisCodeSet}}</td>
                                            <td>{{item.dxQualifier}}</td>
                                            <td>{{item.dxIndex}}</td>
                                            <td>{{item.procedure}}</td>
                                            <td>{{item.modifier}}</td>
                                            <td>{{item.admissionDate | date: 'MM/dd/yyyy'}}</td>
                                            <td>{{item.dischargeDate | date: 'MM/dd/yyyy'}}</td>
                                            <td>{{item.downloadedBy}}</td>
                                            <td>{{item.downloadedOn | date: 'MM/dd/yyyy'}}</td>
                                            <td>{{item.batchNumber}}</td>
                                        </tr>
                                    </tbody>
                                    <tfoot>
                                        <tr *ngIf="totalCount>1">
                                            <td colspan="5" class="m-0 p-0" style="background: white !important;">
                                                <pagination-controls previousLabel="" nextLabel=""
                                                (pageChange)="p = $event"></pagination-controls>
                                            </td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>

                </div>

            </div>
        </div>
        <!-- Area Chart Ends -->

    </div>


</div>
<!-- Begin Page Content Ends -->