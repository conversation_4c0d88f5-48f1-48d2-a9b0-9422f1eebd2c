<!-- Page Content Begin -->
<div class="container-fluid">
    <div class="row">
        <div class="col px-0">
            <div class="card shadow border-blue">
                <!-- Card Header - Dropdown -->
                <div class="card-header-new px-0 py-2" style="background: #0169ab;">
                    <div class="col-12 position-relative vertical-align-middle">
                        <div class="mx-auto row">
                            <div class="col-6 col-md-2 p-1">
                                <button class="btn btn-secondary btn-block" [routerLink]="[backUrl]"
                                    [state]="{filterObj:filterObj}">Back</button>
                            </div>
                            <div class="col-6 col-md-2 p-1">
                                <button class="btn btn-info btn-block" [routerLink]="['/physician/start-new-encounter']"
                                    [state]="{patient:patient,encounterSeenDate:encounterSeenDate,backUrl:backUrl,facilityType:patient.isPrimeFacility,filterObj:filterObj}">Start
                                    New Encounter</button>
                            </div>
                            <div class="col-6 col-md-2 p-1">
                                <button class="btn btn-outline-info btn-block" (click)="viewHistory(patient)">View
                                    History</button>
                            </div>
                            <div class="col-6 col-md-2 p-1">
                                <button class="btn btn-outline-info btn-block"
                                    (click)="openNotes(patient)">Notes({{notesCount}})</button>
                            </div>
                            <div class="col-6 col-md-2 p-1">
                                <button class="btn btn-outline-info btn-block" [title]="attachementLastUploadedDate ? 'Last Uploaded on ' + attachementLastUploadedDate : ''"
                                    (click)="getAttachments(patient)">
                                    Attachments({{attachementCount}})
                                </button>
                            </div>
                        </div>
                    </div>
                </div>


                <div class="card-body px-2 py-1">
                    <!-- table Card starts -->
                    <div class="row mx-0 my-1">
                        <div class="col-12 px-0">
                            <!-- table start -->
                            <div>
                                <table class="table mb-0 customDataTable width-100per" border="1">
                                    <thead>
                                        <tr>
                                            <th class="text-center pb-0">Patient Name</th>
                                            <th class="text-center pb-0">Account No#</th>
                                            <th class="text-center pb-0">Facility</th>
                                            <th class="text-center pb-0">Attending Physician</th>
                                            <th class="text-center pb-0">CPT Codes</th>
                                            <th class="text-center pb-0">ICD Codes</th>
                                            <th class="text-center pb-0">Encounter Seen Date</th>
                                            <th class="text-center pb-0">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr *ngFor="let item of listOfPendingEncounters">
                                            <td class="text-left text-md-center pb-0" data-th="Patient Name"><span
                                                    class="mobile-text">{{patient.patient_Name}}</span></td>
                                            <td class="text-left text-md-center pb-0" data-th="Account No#"><span
                                                    class="mobile-text">{{patient.account_Number}}</span></td>
                                            <td class="text-left text-md-center pb-0" data-th="Facility"><span
                                                    class="mobile-text">{{patient.facility_Name}}</span></td>
                                            <td class="text-left text-md-center pb-0" data-th="Attending Provider"><span
                                                    class="mobile-text">{{item.physicianname}}</span></td>
                                            <td class="text-left text-md-center pb-0" data-th="CPT Codes">
                                                <ul class="mobile-code-list">
                                                    <li *ngFor="let cpt of item.listofTestCPTS" class="mobile-code-item">
                                                        <!-- <span class="mobile-bullet">•</span> -->
                                                        <span class="mobile-text mobile-code-text">{{cpt}}</span>
                                                    </li>
                                                </ul>
                                            </td>
                                            <td class="text-left text-md-center pb-0" data-th="ICD Codes">
                                                <ul class="mobile-code-list">
                                                    <li *ngFor="let icd of item.listofTestICDS" class="mobile-code-item">
                                                        <!-- <span class="mobile-bullet">•</span> -->
                                                        <span class="mobile-text mobile-code-text">{{icd}}</span>
                                                    </li>
                                                </ul>
                                            </td>
                                            <td class="text-left text-md-center pb-0" data-th="Encounter Seen Date">
                                                <span class="mobile-text">{{item.encounterseendateparam | date:
                                                    'MM/dd/yyyy hh:mm:ss a'}}</span></td>
                                            <td class="text-left text-md-center pb-0" data-th="Actions">
                                                <span class="mobile-text">
                                                    <div class="d-flex justify-content-center mobile-actions-container">
                                                        <a class="mobile-action-btn" title="Delete Encounter"
                                                            (click)='deleteEncounter(item)'>
                                                            <i class="far fa-trash-alt text-danger"></i>
                                                        </a>
                                                        <a class="mobile-action-btn" title="Approve Encounter"
                                                            *ngIf="residentAccess=='NO'" (click)='approveEncounter(item)'>
                                                            <i class="fas fa-check-double text-success"></i>
                                                        </a>
                                                        <a class="mobile-action-btn" [routerLink]="['/physician/start-new-encounter']"
                                                            [state]="{patient:patient,encounterSeenDate:item.encounterseendateparam,backUrl:'/physician/pending-approval-encounters',facilityType:'patient.isPrimeFacility',encounterId: item.encounteR_ID,filterObj:filterObj}"
                                                            title="Edit Encounter">
                                                            <i class="far fa-edit text-primary"></i>
                                                        </a>
                                                    </div>
                                                </span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <!-- table end -->
                        </div>
                    </div>
                    <!-- table card ends-->

                </div>



            </div>
        </div>
    </div>
</div>
<!-- Page Content Ends -->

<!-- send note Popup Starts -->
<app-send-note [PatientObject]='patient' [listOfUsersAndGroups]="listOfUsersAndGroups" [lisfOfSentNotes]="lisfOfSentNotes"
    [selectedUsersAndGroups]="selectedUsersAndGroups" (eventListOfNotes)="openNotes(patient)"></app-send-note>
<!-- send note Popup Ends -->

<!-- attachment popup starts -->
<app-upload-attachment  (eventUpdateCount)="updateAttCount(patient)"
    [lisfOfAttachments]="lisfOfAttachments" [PatientObject]="patient" [userType]="'PHYSICIAN'"></app-upload-attachment>
<!-- attachment popup ends -->

<!---- App View History Start-->
<app-view-history [listOfPatientHistory]='listOfPatientHistory' [patient]='patient'
    [historyTotalCount]="historyTotalCount"></app-view-history>
<!---- App View History End-->