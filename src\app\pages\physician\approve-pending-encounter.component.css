/* Mobile-specific styles for action buttons */
.mobile-actions-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    margin-left: 0;
    padding: 5px 0;
}

.mobile-action-btn {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    min-width: 32px;
    min-height: 32px;
    border-radius: 4px;
    background-color: transparent;
    transition: all 0.2s ease;
    cursor: pointer;
    margin: 0 5px;
}

.mobile-action-btn:active {
    transform: scale(0.95);
}


.mobile-action-btn i {
    font-size: 1rem;
}

/* Media queries for mobile devices */
@media (max-width: 767.98px) {
    /* Override the default mobile-text margin for action buttons */
    td[data-th="Actions"] .mobile-text {
        margin-left: 0 !important;
        display: block;
        width: 100%;
        min-width: 100%;
    }


    .mobile-action-btn {
        margin: 0 8px;
    }
}

/* Android-specific fixes */
@media only screen and (max-width: 767.98px) and (-webkit-min-device-pixel-ratio: 1.5) {
    .mobile-action-btn {
        /* Smaller action buttons */
        min-width: 32px;
        min-height: 32px;
        /* Add specific Android tap highlight color */
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
    }

    /* Fix for Android touch events */
    .mobile-actions-container {
        touch-action: manipulation;
    }

    /* Ensure right border is visible on Android */
    table.customDataTable tbody td {
        border-right: 1px solid #17a2b8 !important;
    }

    /* Space between CPT and ICD codes on Android */
    td[data-th="CPT Codes"] {
        padding-bottom: 5px !important;
    }

    td[data-th="ICD Codes"] {
        padding-top: 5px !important;
    }
}

/* Styles for CPT and ICD code lists */
.mobile-code-list {
    padding-left: 0;
    list-style-type: none;
    margin: 0;
}

.mobile-code-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 3px;
}

.mobile-bullet {
    display: inline-block;
    margin-right: 5px;
    color: #000;
    font-size: 16px;
    line-height: 1;
    width: 10px;
    flex-shrink: 0;
}

.mobile-code-text {
    display: inline-block;
    word-break: break-word;
    max-width: calc(100% - 15px);
    text-align: left;
    padding-left: 0;
}


@media (max-width: 767.98px) {
    table.customDataTable tbody td {
        border-right: 1px solid #17a2b8 !important;
    }

    /* Align CPT and ICD codes properly */
    td[data-th="CPT Codes"] .mobile-text,
    td[data-th="ICD Codes"] .mobile-text {
        margin-left: 40vw !important;
        text-align: left;
        padding-left: 0;
    }

  
    /* Add space between CPT and ICD code sections */
    td[data-th="CPT Codes"] {
        padding-bottom: 5px !important;
    }

    td[data-th="ICD Codes"] {
        padding-top: 5px !important;
    }
}

/* iPhone-specific fixes */
@media only screen and (max-width: 428px) and (-webkit-min-device-pixel-ratio: 2) {
    .mobile-actions-container {
        padding: 5px 0;
    }

    .mobile-action-btn {
        min-width: 32px;
        min-height: 32px;
    }

    /* Fix for CPT and ICD code popups on iPhone */
    td[data-th="CPT Codes"] .mobile-text,
    td[data-th="ICD Codes"] .mobile-text {
        margin-left: 40vw !important;
        text-align: left;
        padding-left: 0;
    }

    .mobile-code-list {
        margin: 0;
        width: 100%;
        text-align: left;
    }

    .mobile-code-text {
        font-size: 14px;
        padding: 2px 0;
        margin: 0;
    }

    /* Ensure spacing between CPT and ICD codes on iPhone */
    td[data-th="CPT Codes"] {
        padding-bottom: 5px !important;
    }

    td[data-th="ICD Codes"] {
        padding-top: 5px !important;
    }

    /* Style bullets on iPhone */
    .mobile-bullet {
        color: #000;
        font-size: 18px;
        margin-right: 8px;
        margin-top: -2px;
    }
}
