    <div class="container">
        <div class=" bg-light" *ngIf="!device" [formGroup]="FilterForm">
            <div class="row mx-auto mb-3">
                <div class="col-12"  style="background: #0169ab !important;">
                    <h3 class="h3 text-white pt-2">Medical Calendar</h3>
                </div>
            </div>
           
            <div class="row mx-auto mt-2">
                <div class="col-1 px-1">
                    <div class="form-group">
                        <label for="viewSelect" class="m-auto text-secondary">View {{viewType}}</label>
                        <select class="form-control" id="viewSelect" (change)="selectedView($any($event.target).value)" formControlName="ddlViewType" [(ngModel)]="viewType">
                            <option selected value="Week">Week</option>
                           <option value="Month">Month</option>
                        </select>
                    </div>
                </div>
                <div class="col-2 px-1">
                    <div class="form-group">
                      <label for="dateRangeId" class="m-auto text-secondary">Date</label>
                      <input type="text" formControlName="dateRangeLable" ngxDaterangepickerMd [(ngModel)]="dateRangeLable"
                        [showCustomRangeLabel]="true" [alwaysShowCalendars]="false" [ranges]="ranges" (change)="selectedDate($event)"
                        [showRangeLabelOnInput]="true" [showDropdowns]="true" title="Choose Date Range"
                        placeholder="Choose Date Range" class="form-control btn-lavender" readonly />
                    </div>
                </div>
                <div class="col-2 px-1">
                    <div class="form-group">
                        <label for="viewSelect" class="m-auto text-secondary">Facility</label>
                        <select id="ddlFacility" class="form-control" formControlName="ddlFacility"
                            (change)="getGroupsByFacility($any($event.target).value)">
                            <option [value]="s.facilityName" *ngFor="let s of listOfFacilities">
                                <span>{{s.facilityName}}</span>
                            </option>
                        </select>
                    </div>
                </div>
                <div class="col-2 px-1">
                    <div class="form-group">
                        <label for="viewSelect" class="m-auto text-secondary">Physician Groups</label>
                        <select id="ddlGroups" class="form-control custom-control" formControlName="ddlGroups"
                            (change)="getPhysiciansByGroup()">
                            <option value="All">All Groups</option>
                            <option [value]="s.group_id" *ngFor="let s of listOfGroups">
                                {{s.group_name}}
                            </option>
                        </select>
                    </div>
                </div>
                <div class="col-2 px-1">
                    <div class="form-group">
                        <label for="viewSelect" class="m-auto text-secondary">Physicians</label>
                        <select id="ddlPhysician" class="form-control custom-control" formControlName="ddlPhysician">
                            <option value="All">All Physicians</option>
                            <option [value]="s.physicianEmaiId" *ngFor="let s of listOfPhysicians">
                                {{s.physicianName}}
                            </option>
                        </select>
                    </div>
                </div>
                <div class="col-2 px-1">
                    <div class="form-group">
                        <label for="search" class="m-auto text-secondary">Search</label>
                        <input type="text" placeholder="Name DOB MRN SSN Acc.No #" class="form-control" id="search"
                        [(ngModel)]="search" [ngModelOptions]="{standalone: true}">
                    </div>
                </div>
                <div class="col-1 px-1">
                    <label for="search" class="m-auto text-secondary">&nbsp;</label>
                    <button type="button" class="btn btn-primary btn-block" id="showBtn"
                        (click)="fetchData(1)">Show</button>
                </div>
            </div>
           

            <div class="row mx-auto">
                <div class="col-6 px-1">
                    <h3 class="h6 mt-2 font-weight-bold text-secondary text-right">({{arrivalLabel}})</h3>
                </div>
                <div class="col-6 px-1" >
                    <div class="btn-group float-right" aria-label="Basic example" id="monthButtons">
                        <button type="button" class="btn btn-primary min-width-120" id="prevBtn" [attr.disabled]="isPreviousView ? isPreviousView : null"  
                            (click)="previous()">Previous</button>
                        <button type="button" class="btn btn-white p-1" id="todayBtn"
                            ></button>
                        <button type="button" class="btn btn-primary min-width-120" id="nextBtn"  [attr.disabled]="isNextView ? isNextView : null"  
                            (click)="next()">Next</button>
                    </div>
                </div>
            </div>
            <div class="row mx-auto ">
              <div class="col-12 px-1">
                  <div  class="row mx-auto">
                    <div class="col-6 px-1">
                      <img alt="" src="../../../assets/info-icon.svg" width="15" /><span class="aria-label m-1"><b> IHC Code Missing</b> : Initial Hospital care code is missing on the date of admission</span><br />
                      <img alt="" src="../../../assets/info-icon.svg" width="15" /><span class="aria-label m-1"><b> Duplicate CPT</b> : Cannot have same cpt code submitted on the same date of service</span>
                    </div>
                    <div class="col-6 px-1">
                      <img alt="" src="../../../assets/info-icon.svg" width="15" /><span class="aria-label m-1"><b> FUCPT Missing </b> : Follow up codes are missing</span><br />
                      <img alt="" src="../../../assets/info-icon.svg" width="15" /><span class="aria-label m-1"><b> DSCH Code Missing</b> : Discharge cpt/hcpcs code is missing on the day of discharge</span>
                    </div>
                  </div>
                  <div class="outer">
                    <div class="inner">
                      <table id="weekView" class="wintable table-bordered week-view bg-white">
                        <thead>
                          <tr id="weekViewHeader" class="bg-secondary text-white">
                            <th class="fix text-white"  style="background: #0169ab !important;">
                              <div class="row m-2 font-small">
                                <div class="col-6 p-0">
                                  <div class="form-check form-check-inline m-auto">
                                    <input formControlName="chkRule1" class="form-check-input"
                                           type="checkbox" id="validCharges" (change)="chkChangeEvent($event,1)" value="Valid Charges">
                                    <label class="form-check-label" for="validCharges">IHC Code Missing</label>
                                  </div>
                                  <div class="form-check form-check-inline">
                                    <input formControlName="chkRule2" class="form-check-input"
                                           type="checkbox" id="NoAdmitDischarge" (change)="chkChangeEvent($event,2)" value="No Admin/Dischag">
                                    <label class="form-check-label" for="NoAdmitDischarge">Duplicate CPT</label>
                                  </div>
                                </div>
                                <div class="col-6 p-0">
                                  <div class="form-check form-check-inline m-auto">
                                    <input formControlName="chkRule3" class="form-check-input"
                                           type="checkbox" id="inlineCheckbox1" (change)="chkChangeEvent($event,3)" value="Wrong Charges">
                                    <label class="form-check-label" for="inlineCheckbox1">FUCPT Missing</label>
                                  </div>
                                  <div class="form-check form-check-inline">
                                    <input formControlName="chkRule4" class="form-check-input"
                                           type="checkbox" id="inlineCheckbox2" (change)="chkChangeEvent($event,4)" value="Missing Charges">
                                    <label class="form-check-label" for="inlineCheckbox2">DSCH Code Missing</label>
                                  </div>
                                </div>
                              </div>
                            </th>
                            <th class="text-center" *ngFor="let wdate of listOfColumns">
                              <div class="text-center font-medium">{{wdate.monthyear}}</div>
                              <div class="text-center m-2 font-xxx-large">{{wdate.day}}</div>
                              <div class="text-center font-medium">{{wdate.weekday}}</div>
                            </th>
                          </tr>
                        </thead>

                        <tbody id="weekViewBody">
                          <tr class="patient-col" *ngFor="let ptn of listOfPatients | gridFilter:{Patient_Name:search,Admission_Date:search,Patient_MRN:search,Discharge_Date:search}:false">
                            <td class="day-body-col fix">
                              <div class="row mx-auto mb-2 font-medium">
                                <div class="col-6 p-0 font-weight-bold text-info">
                                  <button type="button" class="btn btn-link p-0 text-left" (click)="getCalendarDataByPatient(ptn.PatientAccountNumber,ptn.Patient_Name)">
                                    {{ptn.Patient_Name}}
                                  </button>
                                </div>
                                <div *ngIf="ptn.Discharge_Date!='NONE'" class="col-6 p-0 font-weight-bold text-danger">
                                  Discharged
                                </div>
                              </div>
                              <div class="row m-auto font-small">
                                <div class="col-6 p-0">
                                  DOB: <span class="font-weight-bold">{{ptn.DOB|date:'MM/dd/yyyy'}}</span>
                                </div>
                                <div class="col-6 p-0">
                                  Admitted: <span class="font-weight-bold">{{ptn.Admission_Date|date:'MM/dd/yy'}}</span>
                                </div>
                              </div>
                              <div class="row m-auto font-small">
                                <div class="col-6 p-0">
                                  MRN: <span class="font-weight-bold">{{ptn.Patient_MRN}}</span>
                                </div>
                                <div class="col-6 p-0">
                                  Discharged: <span *ngIf="ptn.Discharge_Date!='NONE'" class="font-weight-bold">{{ptn.Discharge_Date|date:'MM/dd/yy'}}</span>
                                </div>
                              </div>
                            </td>
                            <td *ngFor="let col of listOfColumns">
                              <div class="min-height-10vh" [innerHTML]='ptn[col.cptColoName]'>
                              </div>

                            </td>
                          </tr>
                        </tbody>

                      </table>
                     <div *ngIf="listOfPatients.length==0" class="font-weight-bold mt-2 text-center text-secondary"><h2>No records found.</h2></div>
                    </div>
                    </div>
                  </div>
               
                <!-- Modal -->
                <div class="modal fade" id="staticBackdrop" data-backdrop="static" data-keyboard="false" tabindex="-1"
                     aria-labelledby="staticBackdropLabel" aria-hidden="true">
                  <div class="modal-dialog modal-xl modal-dialog-centered modal-dialog-scrollable">
                    <div class="modal-content">
                      <div class="modal-header">
                        <h5 class="modal-title" id="staticBackdropLabel">{{PatientName}}</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                          <span aria-hidden="true">&times;</span>
                        </button>
                      </div>
                      <div class="monthly-popup" id="monthViewPopup">
                        <div class="modal-body">
                          <div class="row m-auto">
                            <div class="col-5 px-1 ">

                            </div>
                            <div class="col-2 px-1 text-center">
                              <h3 class="h3 font-weight-bold text-secondary">{{ startDateP | date: 'MMM yyyy'}}</h3>
                            </div>
                            <div class="col-5 px-1">
                              <div class="btn-group float-right d-block">
                                <div id="monthButtons" class="btn-group float-right">
                                  <button type="button" id="prevBtn"
                                          class="btn btn-primary min-width-120" (click)="previousP()">
                                    Previous
                                  </button>
                                  <button type="button" id="todayBtn" class="btn btn-white p-1"></button>
                                  <button type="button" id="nextBtn"
                                          class="btn btn-primary min-width-120" (click)="nextP()">
                                    Next
                                  </button>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div class="row mx-auto my-2">
                            <div class="col-12 p-0">
                              <table class="table table-bordered week-view bg-white">
                                <thead>
                                  <tr class="bg-secondary text-white">
                                    <th class="text-center">
                                      <div class="text-center m-2 font-x-large">SUN</div>
                                      <div class="text-center font-medium"></div>
                                    </th>
                                    <th class="text-center">
                                      <div class="text-center m-2 font-x-large">MON</div>
                                      <div class="text-center font-medium"></div>
                                    </th>
                                    <th class="text-center">
                                      <div class="text-center m-2 font-x-large">TUE</div>
                                      <div class="text-center font-medium"></div>
                                    </th>
                                    <th class="text-center">
                                      <div class="text-center m-2 font-x-large">WED</div>
                                      <div class="text-center font-medium"></div>
                                    </th>
                                    <th class="text-center">
                                      <div class="text-center m-2 font-x-large">THU</div>
                                      <div class="text-center font-medium"></div>
                                    </th>
                                    <th class="text-center">
                                      <div class="text-center m-2 font-x-large">FRI</div>
                                      <div class="text-center font-medium"></div>
                                    </th>
                                    <th class="text-center">
                                      <div class="text-center m-2 font-x-large">SAT</div>
                                      <div class="text-center font-medium"></div>
                                    </th>
                                  </tr>
                                </thead>
                                <tbody>
                                  <tr class="patient-col ng-star-inserted" *ngFor="let ptn of Patients">
                                    <td>
                                      <div class="position-relative m-auto date-box" [innerHTML]='ptn.sunday'>
                                      </div>
                                    </td>
                                    <td>
                                      <div class="position-relative m-auto date-box" [innerHTML]='ptn.monday'>
                                      </div>
                                    </td>
                                    <td>
                                      <div class="position-relative m-auto date-box" [innerHTML]='ptn.tuesday'>
                                      </div>
                                    </td>
                                    <td>
                                      <div class="position-relative m-auto date-box" [innerHTML]='ptn.wednesday'>
                                      </div>
                                    </td>
                                    <td>
                                      <div class="position-relative m-auto date-box" [innerHTML]='ptn.thursday'>
                                      </div>
                                    </td>
                                    <td>
                                      <div class="position-relative m-auto date-box" [innerHTML]='ptn.friday'>
                                      </div>
                                    </td>
                                    <td>
                                      <div class="position-relative m-auto date-box" [innerHTML]='ptn.saturday'>
                                      </div>
                                    </td>
                                  </tr>
                                </tbody>
                              </table>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

              </div>

        <div class="bg-light" *ngIf="device" [formGroup]="FilterForm">
            <div class="row mx-auto mb-2 bg-secondary align-items-center">
                <div class="col-10">
                    <h3 class="h3 text-white pt-2">Medical Calendar</h3>
                </div>
                <div class="col-2">
                    <div class="text-white px-2 py-1 border rounded m-auto filter-icon-wrapper">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                            <path
                                d="M488 0H24C2.7 0-8 25.9 7.1 41L192 225.9V432c0 7.8 3.8 15.2 10.2 19.7l80 56C298 518.7 320 507.5 320 488V225.9l184.9-185C520 25.9 509.3 0 488 0z" />
                        </svg>
                    </div>
                </div>
            </div>
            <div class="row mx-2 mt-2 d-none" id="filtersView">
                <div class="col-12 col-xs-1 col-md-1 px-1">
                    <div class="form-group">
                        <label for="viewSelect" class="m-auto text-secondary font-weight-bold">View {{viewType}}</label>
                        <select class="form-control" id="viewSelect" formControlName="ddlViewType" [(ngModel)]="viewType">
                            <option selected value="Week">Week</option>
                            <option value="Month">Month</option>
                        </select>
                    </div>
                </div>
                <div class="col-12 col-xs-2 col-md-2 px-1">
                    <div class="form-group">
                        <label for="dateRangeId" class="m-auto text-secondary font-weight-bold">Date</label>
                        <div id="dateRangeId" class="custom-date-range" (ngModelChange)="selectedDate($event)" [(ngModel)]="arrivalLabel">
                            <i class="fa fa-calendar"></i>&nbsp;
                            <span></span> <i class="fa fa-caret-down"></i>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-xs-2 col-md-2 px-1">
                    <div class="form-group">
                        <label for="ddlFacility" class="m-auto text-secondary font-weight-bold">Facility</label>
                        <select class="form-control" id="ddlFacility" formControlName="ddlFacility"
                        (change)="getGroupsByFacility($any($event.target).value)">
                        <option [value]="s.facilityName" *ngFor="let s of listOfFacilities">
                            <span>{{s.facilityName}}</span>
                        </option>
                        </select>
                    </div>
                </div>
                <div class="col-12 col-xs-2 col-md-2 px-1">
                    <div class="form-group">
                        <label for="ddlGroups" class="m-auto text-secondary font-weight-bold">Physician Groups</label>
                        <select id="ddlGroups" class="form-control custom-control" formControlName="ddlGroups"
                        (change)="getPhysiciansByGroup()">
                        <option value="All">All Groups</option>
                        <option [value]="s.group_id" *ngFor="let s of listOfGroups">
                            {{s.group_name}}
                        </option>
                    </select>
                    </div>
                </div>
                <div class="col-12 col-xs-2 col-md-2 px-1">
                    <div class="form-group">
                        <label for="ddlPhysician" class="m-auto text-secondary font-weight-bold">Physicians</label>
                        <select id="ddlPhysician" class="form-control custom-control" formControlName="ddlPhysician">
                            <option value="All">All Physicians</option>
                            <option [value]="s.physicianEmaiId" *ngFor="let s of listOfPhysicians">
                                {{s.physicianName}}
                            </option>
                        </select>
                    </div>
                </div>
                <div class="col-12 col-xs-2 col-md-2 px-1">
                    <div class="form-group">
                        <label for="search" class="m-auto text-secondary font-weight-bold">Search</label>
                        <input type="text" placeholder="Name DOB MRN SSN Acc.No #" class="form-control" id="search">
                    </div>
                </div>
                <div class="col-6    col-xs-1 col-md-1 px-1">
                    <button type="button" class="btn btn-info btn-block" id="cancelBtn">
                        Cancel
                    </button>
                </div>
                <div class="col-6 col-xs-1 col-md-1 px-1">
                    <button type="button" class="btn btn-secondary btn-block" id="showBtn"
                    (click)="fetchData(1)">APPLY</button>
                </div>

            </div>

            <div class="row mx-auto mt-1 px-1 {{isPatientView ? '' : 'd-none'}}" id="patientsView">
                <div class="col-12 col-xs-10 col-md-10 px-1 my-2" id="monthButtons">
                    <div class="btn-group row mx-auto w-100" aria-label="Basic example">
                        <button type="button" class="btn btn-secondary col-4" id="prevBtn"  [attr.disabled]="isPatientView" (click)="previous()">Previous</button>
                        <button type="button" class="btn btn-info col-4" id="todayBtn" ></button>
                        <button type="button" class="btn btn-secondary col-4" id="nextBtn" [attr.disabled]="isNextView" (click)="next()">Next</button>
                    </div>
                </div>
                <div class="col-12 col-xs-2 col-md-2 px-1">
                    <h6 class="h6 font-weight-bold text-secondary text-center mb-2 mt-0 dateRange" id="dateRange">&nbsp;</h6>
                </div>
                <div class="col-12 px-1">
                    <div class="outer">
                        <table id="weekView" class="wintable table-bordered week-view bg-white m-auto">
                            <thead>
                                <tr id="weekViewHeader" class="bg-secondary text-white"><th>
                                    <div class="row m-auto font-small">
                                    <div class="col-6 p-0">
                                        <div class="form-check form-check-inline m-auto">
                                            <input class="form-check-input" type="checkbox" id="validCharges" value="Valid Charges" formControlName="chkRule1" >
                                            <label class="form-check-label" for="validCharges">Valid Charges</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="checkbox" id="NoAdmitDischarge" value="No Admin/Dischag" formControlName="chkRule2" >
                                            <label class="form-check-label" for="NoAdmitDischarge">No Admin/Dischage</label>
                                        </div>
                                    </div>
                                    <div class="col-6 p-0">
                                        <div class="form-check form-check-inline m-auto">
                                            <input class="form-check-input" type="checkbox" id="inlineCheckbox1" value="Wrong Charges" formControlName="chkRule3" >
                                            <label class="form-check-label" for="inlineCheckbox1">Wrong Charges</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="checkbox" id="inlineCheckbox2" value="Missing Charges" formControlName="chkRule4" >
                                            <label class="form-check-label" for="inlineCheckbox2">Missing Charges</label>
                                        </div>
                                    </div>
                                    </div></th>
                                </tr>
                            </thead>

                            <tbody id="weekViewBody">
                                <tr class="patient-col"><td class="day-body-col" *ngFor="let ptn of listOfPatients">
                                    <div class="row mx-auto mb-2 font-medium text-secondary">
                                    <div class="col-6 p-0 font-weight-bold text-info">
                                        <button type="button" class="btn btn-link p-0" (click)="showPatientCalendar()">
                                            {{ptn.Patient_Name}}
                                        </button>
                                    </div>
                                    <div class="col-6 p-0 font-weight-bold text-danger">Discharge</div>
                                    </div>
                                    <div class="row m-auto font-small text-secondary">
                                    <div class="col-6 p-0">DOB: <span class="font-weight-bold">{{ptn.DOB|date:'MM/dd/yyyy'}}</span></div>
                                    <div class="col-6 p-0">Admit: <span class="font-weight-bold">{{ptn.Admission_Date|date:'MM/dd/yyyy'}}</span></div>
                                    </div>
                                    <div class="row m-auto font-small text-secondary">
                                    <div class="col-6 p-0">MRN: <span class="font-weight-bold">{{ptn.Patient_MRN}}</span></div>
                                    <div class="col-6 p-0">Discharge: <span class="font-weight-bold">{{ptn.Discharge_Date|date:'MM/dd/yyyy'}}</span></div>
                                    </div>
                                    <div class="row m-auto font-small text-secondary">
                                    <div class="col-6 p-0">Attende: <span class="font-weight-bold">N. Acharya</span></div>
                                    <div class="col-6 p-0 text-right"><button class="btn btn-sm btn-secondary py-0 w-50" (click)="showPatientCalendar()">View</button></div>
                                    </div></td></tr>
                            </tbody>
                        </table>
                    </div>
                    <!-- Week view will be populated by JavaScript -->
                </div>
            </div>
            <div class="container text-center {{isPatientView ? 'd-none' : ''}}" id="attendeesView">
                <div class="close-button" id="clearBtn">
                        <img src="../../../assets/close.svg" alt="">
                </div>
                <div class="row mx-auto mb-2">
                    <div class="col-12 col-xs-2 col-md-2 px-1"></div>
                    <h3 class="h3 mt-2 font-weight-bold text-secondary text-center m-auto" id="attendeeName">James Kemp</h3>
                </div>
                <div class="col-12 col-xs-2 col-md-2 px-1">
                    <h6 class="h6 mt-2 font-weight-bold text-secondary dateRange" id="dateRange">(11/10/2024 to 11/16/2024)</h6>
                </div>
               
                <div class="row mx-auto my-auto">
                    <div id="calendarCarousel" class="carousel slide w-100" data-ride="carousel" data-interval="false">
                        <div class="carousel-inner">
                            <div class="carousel-item active">
                                <div class="row mx-auto">
                                    <div class="col-4 p-0">
                                        <div class="card border-0 bg-transparent">
                                            <div class="card-head text-white bg-secondary">
                                                <div class="text-center font-xxx-large" id="SunDate">10</div>
                                                <div class="text-center font-medium">Sun</div>
                                            </div>
                                            <div class="card-body p-0">
                                                <div id="SunPatient"><div class="patient"></div></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-4 p-0">
                                        <div class="card border-0 bg-transparent">
                                            <div class="card-head text-white bg-secondary">
                                                <div class="text-center font-xxx-large" id="MonDate">11</div>
                                                <div class="text-center font-medium">Mon</div>
                                            </div>
                                            <div class="card-body p-0">
                                                <div id="MonPatient"><div class="patient"></div></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-4 p-0">
                                        <div class="card border-0 bg-transparent">
                                            <div class="card-head text-white bg-secondary">
                                                <div class="text-center font-xxx-large" id="TueDate">12</div>
                                                <div class="text-center font-medium">Tue</div>
                                            </div>
                                            <div class="card-body p-0">
                                                <div id="TuePatient"><div class="patient"></div></div>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                            <div class="carousel-item">
                                <div class="row mx-auto">
                                    <div class="col-4 p-0">
                                        <div class="card border-0 bg-transparent">
                                            <div class="card-head text-white bg-secondary">
                                                <div class="text-center font-xxx-large" id="WedDate">13</div>
                                                <div class="text-center font-medium">Wed</div>
                                            </div>
                                            <div class="card-body p-0">
                                                <div id="WedPatient"><div class="patient"></div></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-4 p-0">
                                        <div class="card border-0 bg-transparent">
                                            <div class="card-head text-white bg-secondary">
                                                <div class="text-center font-xxx-large" id="ThuDate">14</div>
                                                <div class="text-center font-medium">Thu</div>
                                            </div>
                                            <div class="card-body p-0">
                                                <div id="ThuPatient"><div class="patient"></div></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-4 p-0">
                                        <div class="card border-0 bg-transparent">
                                            <div class="card-head text-white bg-info">
                                                <div class="text-center font-xxx-large" id="FriDate">15</div>
                                                <div class="text-center font-medium">Fri</div>
                                            </div>
                                            <div class="card-body p-0">
                                                <div id="FriPatient"><div class="patient"></div></div>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                            <div class="carousel-item">
                                <div class="row mx-auto">
                                    <div class="col-4 p-0">
                                        <div class="card border-0 bg-transparent">
                                            <div class="card-head text-white bg-secondary">
                                                <div class="text-center font-xxx-large" id="SatDate">16</div>
                                                <div class="text-center font-medium">Sat</div>
                                            </div>
                                            <div class="card-body p-0">
                                                <div id="SatPatient"><div class="patient"></div></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <a class="carousel-control-prev" href="#calendarCarousel" data-slide="prev">
                                <span class="carousel-control-prev-icon"></span>
                            </a>
                            <a class="carousel-control-next" href="#calendarCarousel" data-slide="next">
                                <span class="carousel-control-next-icon"></span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>        
        </div>
    </div>
  </div>
