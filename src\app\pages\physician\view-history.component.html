<!-- History popup starts -->
<div class="modal fade" id="viewHistory" tabindex="-1" aria-labelledby="exampleModalCenterTitle" maria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg">
    <div class="modal-content">
      <div class="modal-header py-2">
        <h5 class="modal-title" id="exampleModalCenterTitle">Past Encounters</h5>
        <i class="fa fa-times closePopup" data-dismiss="modal"></i>
      </div>
      <div class="modal-body" id="viewPntHistory">
        <!-- table start -->
        <div class="card-body px-2 py-1">
          <div class="row mx-0">
            <div class="col-12 px-0">
              <div class="table-responsive">
                <table class="table mb-0 width-100per patient-history-table" border="1">
                  <thead>
                    <tr>
                      <th class="text-center">Action</th>
                      <th class="text-center">Patient Name</th>
                      <th class="text-center">Account Number</th>
                      <th class="text-center">Provider</th>
                      <th class="text-center">Date Seen</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let item of listOfPatientHistory">
                      <td class="text-center">
                        <button class="btn btn-outline-info"
                          (click)="viewEncoutnerHistory(item.encounter_id)">View</button>
                      </td>
                      <td class="text-center">{{patient.patient_Name}}</td>
                      <td class="text-center">{{patient.account_Number}}</td>
                      <td class="text-center">{{item.physicianname}}</td>
                      <td class="text-center">Seen: {{item.encounterseendate}}</td>

                      <!-- Mobile-specific merged patient info display -->
                      <td class="mobile-patient-info d-block d-md-none">
                        <div class="patient-name">{{patient.patient_Name}}</div>
                        <div class="patient-details">
                          <span class="account-number">{{patient.account_Number}}</span>
                          <span class="separator">|</span>
                          <span class="provider-name">{{item.physicianname}}</span>
                        </div>
                        <div class="encounter-date"><strong>@Seen: {{item.encounterseendate}}</strong></div>
                      </td>
                    </tr>
                  </tbody>
                  <tr *ngIf="historyTotalCount>historyLimit&&historyTotalCount>listOfPatientHistory.length" class="d-none d-md-table-row">
                    <td colspan="5"><button (click)="viewHistory(patient)" class="btn btn-info float-right">Load
                        More</button></td>
                  </tr>
                </table>

                <!-- Mobile Load More Button -->
                <div *ngIf="historyTotalCount>historyLimit&&historyTotalCount>listOfPatientHistory.length" class="load-more-mobile d-block d-md-none">
                  <button (click)="viewHistory(patient)" class="btn btn-info">Load More</button>
                </div>
              </div>
              <!-- table end -->
            </div>
          </div>
        </div>
      </div>
      <div class="modal-body" id="viewEnvHistory">
        <!-- table Card starts -->
        <div class="row mx-0 my-2">
          <div class="col-12">
            <button class="btn btn-outline-info" (click)="backToPatientHistory()">Back</button>
          </div>
        </div>
        <div class="row mx-0 my-2">
          <div class="col-12">
            <div class="table-responsive">
              <!-- table start -->
              <table class="table mb-0 width-100per" border="1">
                <thead>
                  <tr>
                    <th class="text-center">Patient Name</th>
                    <th class="text-center">Account No#</th>
                    <th class="text-center">Facility</th>
                    <th class="text-center">Age</th>
                    <th class="text-center">Room No</th>
                    <th class="text-center">Patient Type</th>
                    <th class="text-center">Attending Provider</th>
                    <th class="text-center">LOS</th>
                    <th class="text-center">Payer Class</th>
                    <th class="text-center">Admission Date</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td class="text-center">{{patient.patient_Name}}</td>
                    <td class="text-center">{{patient.account_Number}}</td>
                    <td class="text-center">{{patient.facility_Name}}</td>
                    <td class="text-center">{{patient.age }}</td>
                    <td class="text-center">{{patient.room_Number}}</td>
                    <td class="text-center">{{patient.admission_Type}}</td>
                    <td class="text-center">{{patient.attending_Physician_InApp}}</td>
                    <td class="text-center">{{patient.arithmetic_Mean_LOS}}</td>
                    <td class="text-center">{{patient.reimbursement_Type}}</td>
                    <td class="text-center">{{patient.admit_Datetime | date: 'MM/dd/yyyy'}}</td>
                  </tr>
                </tbody>
              </table>
              <!-- table end -->
            </div>
          </div>
        </div>
        <!-- table card ends-->
        <!-- search start -->
        <div class="row mx-0 my-2">
          <div class="col-12">
            <div class="table-responsive">
              <table class="table table-bordered width-100per">
                <thead>
                  <tr>
                    <th>Codes</th>
                    <th *ngFor="let thData of listOfHistory.cptEditHistory" class="bg-gradient-primary text-white">
                      {{thData.role}} {{thData.createddate|date: 'MM/dd/yyyy'}}</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>Cpt Codes</td>
                    <td *ngFor="let cptData of listOfHistory.cptEditHistory; let i=index">
                      <ul class="ml-3">
                        <li *ngFor="let cptData of listOfHistory.cptEditHistory[i]?.cptcodes"
                          tooltip="{{cptData.deletedReson}}" [style.color]="cptData.color">{{cptData.code}}</li>
                      </ul>
                    </td>
                  </tr>
                  <tr>
                    <td>Icd Codes</td>
                    <td *ngFor="let cptData of listOfHistory.icdEditHistory; let i=index">
                      <ul class="ml-3">
                        <li *ngFor="let icdData of listOfHistory.icdEditHistory[i]?.icdcodes"
                          [style.color]="icdData.color">{{icdData.code}}</li>
                      </ul>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
        <!-- search end -->
      </div>
    </div>
  </div>
</div>
<!-- History ends -->