<div class="modal fade" id="editSubgroupModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header py-2">
                <h5 class="modal-title" id="editSubgroupModalCenterTitle">Update Subgroup</h5>
                <i class="fa fa-times closePopup" data-dismiss="modal"></i>
            </div>
            <div class="modal-body" [formGroup]="form">
                <div class="row mx-auto">
                    <div class="col-12 col-md-9 p-1">
                            <select class="form-control custom-control border-blue" formControlName="ddlSubgroup"
                            [(ngModel)]="subGroupName">
                            <option value=''>---Select Subgroup---</option>
                            <option [value]="s.subGroupName" *ngFor="let s of listofSubGroup">
                                {{s.subGroupName}}
                            </option>
                        </select>
                        <div *ngIf="submitted && f['ddlSubgroup'].errors" class="text-danger">
                            <div *ngIf="f['ddlSubgroup'].errors['required']">Subgroup is required</div>
                        </div>
                    </div>
                    <div class="col-12 col-md-3 p-1">
                        <button type="submit" class="btn btn-outline-info btn-block"
                         [disabled]="encounterObj.subGroupName==form.value.ddlSubgroup"  (click)="editSubgroup(encounterObj)">Update</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>