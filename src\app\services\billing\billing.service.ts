import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { EncrDecrServiceService } from '../common/encr-decr-service.service';
import { apiUrl } from 'src/app/config';

@Injectable({
  providedIn: 'root'
})
export class BillingService {

  constructor(private readonly http: HttpClient, private readonly encrDecr: EncrDecrServiceService) { }

  getPatientsForBilling() {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.get(apiUrl + 'api/Billing/AsyncGetPatients', { headers: headers });
  }

  filterBillerPatients(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Billing/AsyncFilterPatientForBiller', request, { headers: headers });
  }

  filterBillerPatientsData(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Billing/AsyncFilterPatientForBillerData', request, { headers: headers });
  }

  getEncountersByPatient(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Billing/AsyncGetEncountersByPatient', request, { headers: headers });
  }

  markAsBilled(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    request.flag = this.encrDecr.set("MARKASBILLED");
    return this.http.post(apiUrl + 'api/Billing/MarkAsBilledUnBilled', request, { headers: headers });
  }

  markAsUnBilled(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    request.flag = this.encrDecr.set("MARKASUNBILLED");
    return this.http.post(apiUrl + 'api/Billing/MarkAsBilledUnBilled', request, { headers: headers });
  }

  markAllAsBilled(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Billing/MarkAllAsBilled', request, { headers: headers });
  }

  editEncounterSeenDate(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Billing/EditEncounterSeenDate', request, { headers: headers });
  }

  removeFromBillingQueue(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Billing/RemoveFromBillingQueue', request, { headers: headers });
  }

  editEncounter(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Billing/EditEncounter', request, { headers: headers });
  }

  sendEncounterToKareo(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Billing/SendEncounterToKareo', request, { headers: headers });
  }

  sendAllEncounterToKareo(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Billing/SendAllEncounterToKareo', request, { headers: headers });
  }

  getModifiers(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Billing/GetModifiers', request, { headers: headers });
  }

  getAlerts(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Billing/GetAlerts', request, { headers: headers });
  }
  sendEncounterToRXNT(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Billing/SendEncounterToRXNT', request, { headers: headers });
  }

  sendAllEncounterToRXNT(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Billing/SendAllEncounterToRXNT', request, { headers: headers });
  }

  sendEncounterToADMD(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Billing/SendEncounterToADMD', request, { headers: headers });
  }

  sendAllEncounterToADMD(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Billing/SendAllEncounterToADMD', request, { headers: headers });
  }
  EditMultipleEncounters(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Billing/EditMultipleEncounters', request, { headers: headers });
  }
  AsyncFetchFacilitiesAndPatientCount() {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Billing/AsyncFetchFacilitiesAndPatientCount', { headers: headers });
  }
  getBillingDataToEpic(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Billing/GetBillingDataToEpic',request, { headers: headers });
  }
  saveHistoryBillingDataToEpic(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Billing/SaveHistoryBillingDataToEpic',request, { headers: headers });
  }

}
