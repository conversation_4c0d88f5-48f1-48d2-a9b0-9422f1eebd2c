<!-- Begin Page Content Starts -->
<div class="container-fluid">
    <!-- Content Row -->
    <div class="row">
        <!-- Area Chart -->
        <div class="col px-0">
            <div class="card shadow border-blue">
                <div class="card-body px-md-3 px-2 pt-md-3 p-2" [formGroup]="patientForm">
                    <div class="row mx-0">
                        <div class="col-12 px-0">
                            <div class="card shadow border-blue">

                                <div class="row mx-0">
                                    <div class="col-12 col-md-6">
                                        <div class="form-group">
                                            <label for="FirstName">First Name</label>
                                            <input class="form-control input-border" maxlength="125"
                                                [ngClass]="{ 'is-invalid': submitted && f['txtFirstName'].errors}"
                                                type="text" formControlName="txtFirstName" id="FirstName"
                                                placeholder="First Name">
                                            <div class="text-danger"
                                                *ngIf="submitted && f['txtFirstName'].errors?.['maxlength']">Max length
                                                125 characters</div>
                                        </div>
                                    </div>
                                    <div class="col-12 col-md-6">
                                        <div class="form-group">
                                            <label for="LastName">Last Name</label>
                                            <input class="form-control input-border" id='txtLastName' maxlength="125"
                                                [ngClass]="{ 'is-invalid': submitted && f['txtLastName'].errors}"
                                                type="text" formControlName="txtLastName" id="LastName"
                                                placeholder="Last Name">
                                            <div class="text-danger"
                                                *ngIf="submitted && f['txtLastName'].errors?.['cannotContainSpace']">
                                                Last Name can not contain space.</div>
                                            <div class="text-danger"
                                                *ngIf="submitted && f['txtLastName'].errors?.['maxlength']">Max length
                                                125 characters</div>
                                        </div>
                                    </div>

                                    <div class="col-12 col-md-6">
                                        <div class="form-group">
                                            <label for="AccountNumber">Account Number</label>
                                            <input class="form-control input-border" maxlength="20" type="text"
                                                formControlName="txtAccountNo" id="AccountNumber"
                                                placeholder="Account Number">
                                        </div>
                                    </div>
                                    <div class="col-12 col-md-6">
                                        <div class="form-group">
                                            <label for="PatientMRN">Patient MRN</label>
                                            <input class="form-control input-border" maxlength="225"
                                                [ngClass]="{ 'is-invalid': submitted && f['txtMRN'].errors}" type="text"
                                                formControlName="txtMRN" id="PatientMRN" placeholder="Patient MRN">
                                        </div>
                                    </div>

                                    <div class="col-12 col-md-6">
                                        <div class="form-group">
                                            <label for="DateofBirth">Date of Birth</label>
                                            <input class="form-control input-border"
                                                [ngClass]="{ 'is-invalid': submitted && f['txtDOB'].errors}" type="date"
                                                formControlName="txtDOB" [max]="maxDate" id="DateofBirth" placeholder="Date of Birth">
                                        </div>
                                    </div>
                                    <div class="col-12 col-md-6">
                                        <div class="form-group">
                                            <label for="PatientClass">Patient Class</label>
                                            <select formControlName="role" class="form-control"
                                                [ngClass]="{ 'is-invalid': submitted && f['ddlPatientClass'].errors}"
                                                formControlName="ddlPatientClass" id="PatientClass">
                                                <option [value]="null">Select Patient Class</option>
                                                <option value="I"><span>InPatient</span></option>
                                                <option value="O"><span>OutPatient</span></option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-12 col-md-6">

                                        <div class="form-group">
                                            <label for="FacilityName">Facility Name</label>
                                            <select formControlName="ddlFacilityName" class="form-control"
                                                [ngClass]="{ 'is-invalid': submitted && f['ddlFacilityName'].errors}"
                                                formControlName="ddlFacilityName"
                                                (change)="getPhysiciansByFacility($any($event.target).value)"
                                                id="FacilityName">
                                                <option value="">Choose Facility Name</option>
                                                <option [value]="s.facilityName" *ngFor="let s of listOfFacilities">
                                                    <span>{{s.facilityName}}</span>
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-12 col-md-6">
                                        <div class="form-group">
                                            <label for="AdmissionDate">Admission Date</label>
                                            <mat-form-field>
                                                <mtx-datetimepicker #datetimePicker6 [type]="type" [mode]="mode"
                                                    [multiYearSelector]="multiYearSelector" [startView]="startView"
                                                    [twelvehour]="twelvehour" [timeInterval]="timeInterval"
                                                    [touchUi]="touchUi" [timeInput]="timeInput">
                                                </mtx-datetimepicker>
                                                <input [mtxDatetimepicker]="datetimePicker6" id="txtaddAdmissionDate"
                                                    formControlName="txtaddAdmissionDate" matInput
                                                    class="form-control input-border"
                                                    [ngClass]="{ 'is-invalid': submitted && f['txtaddAdmissionDate'].errors}">
                                                <mtx-datetimepicker-toggle [for]="datetimePicker6"
                                                    matSuffix></mtx-datetimepicker-toggle>
                                            </mat-form-field>
                                        </div>
                                    </div>

                                    <div class="col-12 col-md-6">
                                        <div class="form-group">
                                            <label for="AssignPatientTo">Assign Patient To</label>
                                            <select formControlName="role" class="form-control"
                                                [ngClass]="{ 'is-invalid': submitted && f['ddlPhysician'].errors}"
                                                formControlName="ddlPhysician" id="AssignPatientTo">
                                                <option value="">Choose Physician</option>
                                                <option [value]="s.physicianName" *ngFor="let s of listOfPhysicians">
                                                    <span>{{s.physicianName}}</span>
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-12 col-md-6">
                                        <div class="form-group">
                                            <label for="Department">Department</label>
                                            <input class="form-control input-border" maxlength="100"
                                                [ngClass]="{ 'is-invalid': submitted && f['txtDepartment'].errors}"
                                                type="text" formControlName="txtDepartment" id="Department"
                                                placeholder="Department">
                                        </div>
                                    </div>

                                    <div class="col-12 col-md-6">
                                        <div class="form-group">
                                            <label for="RoomNumber">Room Number</label>
                                            <input class="form-control input-border" maxlength="100"
                                                [ngClass]="{ 'is-invalid': submitted && f['txtRoomNo'].errors}"
                                                type="text" formControlName="txtRoomNo" id="RoomNumber"
                                                placeholder="Room Number">
                                        </div>
                                    </div>
                                    <div class="col-12 col-md-6">
                                        <div class="form-group">
                                            <label for="BedNumber">Bed Number</label>
                                            <input class="form-control input-border" maxlength="100"
                                                [ngClass]="{ 'is-invalid': submitted && f['txtBedNo'].errors}"
                                                type="text" formControlName="txtBedNo" id="BedNumber"
                                                placeholder="Bed Number">
                                        </div>
                                    </div>
                                    <div class="col-12 col-md-6">
                                        <div class="form-group">
                                            <label for="SSN">SSN</label>
                                            <input class="form-control input-border" maxlength="9" type="text"
                                                formControlName="txtSSN" id="SSN" placeholder="SSN">
                                            <div class="text-danger"
                                                *ngIf="submitted && f['txtSSN'].errors?.['maxLength']">Max length 9
                                                characters</div>
                                            <div class="text-danger"
                                                *ngIf="submitted && f['txtSSN'].errors?.['pattern']">SSN should be a
                                                number</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
                <div class="card-footer px-md-3 pb-md-3 px-2 pb-3">

                    <button class="btn btn-sm btn-outline-info float-right btn-style"
                        (click)="insertPatient()">Submit</button>
                    <button type="button" class="btn btn-sm btn-outline-info float-right btn-style buttonstyle"
                        (click)="clearData()">Cancel</button>
                </div>
            </div>
        </div>
    </div>
</div>