import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BillingRoutingModule } from './billing-routing.module';
import { BillingComponent } from './billing.component';
import { EncounterComponent } from './encounter.component';
import { MarkAsBilledComponent } from './mark-as-billed.component';
import { MarkAsUnbilledComponent } from './mark-as-unbilled.component';
import { MarkAsAllBilledComponent } from './mark-as-all-billed.component';
import { RemoveFromBillingQueueComponent } from './remove-from-billing-queue.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgxPaginationModule } from 'ngx-pagination';
import { FinaliseAllADMDIcdsComponent } from './finalise-all-admd-icds.component';
import { FinaliseAllIcdsComponent } from './finalise-all-icds.component';
import { FinaliseAllRXNTIcdsComponent } from './finalise-all-rxnt-icds.component';
import { CommonTaskModule } from '../common/common.module';
import { TooltipModule } from 'ngx-bootstrap/tooltip';
import { MarkAsAlreadyBilledComponent } from './mark-as-already-billed.component';
import { SharedModule } from 'src/app/shared.module';
import { EpicBillingExportComponent } from './epic-billing-export.component';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';


@NgModule({
  imports: [
    CommonModule,
    BillingRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    NgxPaginationModule,
    NgxPaginationModule,
    CommonTaskModule,
    TooltipModule.forRoot(),
    SharedModule,
     NgMultiSelectDropDownModule.forRoot()
  ],
  declarations: [
    BillingComponent,
    EncounterComponent,
    MarkAsBilledComponent,
    MarkAsUnbilledComponent,
    MarkAsAllBilledComponent,
    RemoveFromBillingQueueComponent,
    FinaliseAllRXNTIcdsComponent,
    FinaliseAllADMDIcdsComponent,
    FinaliseAllIcdsComponent,
    MarkAsAlreadyBilledComponent,
    EpicBillingExportComponent,
  ],
  providers: [],
  bootstrap: [BillingComponent]

})
export class BillingModule { }
