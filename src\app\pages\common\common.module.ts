import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ChartModule } from 'angular-highcharts';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { BsDatepickerModule } from 'ngx-bootstrap/datepicker';
import { NgxPaginationModule } from 'ngx-pagination';
import { CommonTaskRoutingModule } from './common-routing.module';
import { AddPatientComponent } from './add-patient.component';
import { ViewEncounterHistoryComponent } from './view-encounter-history.component';
import { EditPatientComponent } from './edit-patient.component';
import { ReplaceICDCodesComponent } from './replace-icdcodes.component';
import { UndoDischargePatientComponent } from './undo-discharge-patient.component';
import { UploadAttachmentComponent } from './upload-attachment.component';
import { SendNoteComponent } from './send-note.component';
import { AddMissingEncounterComponent } from './add-missing-encounter.component';
import { SubmitMissingEncounterComponent } from './submit-missing-encounter.component';
import { EditEncounterSeenDateComponent } from './edit-encounter-seen-date.component';
import { RemoveReasonsCPTComponentComponent } from './remove-reasons-cptcomponent.component';
import { TooltipModule } from 'ngx-bootstrap/tooltip';
import { MtxNativeDatetimeModule } from '@ng-matero/extensions/core';
import { MtxDatetimepickerModule } from '@ng-matero/extensions/datetimepicker';
import { MatInputModule } from '@angular/material/input';
import { CptCodeComponent } from './cpt-code.component';
import { SharedModule } from 'src/app/shared.module';
import { IcdCodeComponent } from './icd-code.component';
import { CptModifiersComponent } from './cpt-modifiers.component';
import { AlertComponent } from './alert.component';
import { DeleteEncounterComponent } from './delete-encounter.component';
import { EditDischargeDateComponent } from './edit-discharge-date.component';
import { SearchPatientsComponent } from './search-patients.component';
import { ActionsOfViewModifyEncounterComponent } from './actions-of-view-modify-encounter.component';
import { EditSubgroupComponent } from './edit-subgroup.component';


@NgModule({
  declarations: [
    AddPatientComponent,
    ViewEncounterHistoryComponent, 
    EditPatientComponent, 
    ReplaceICDCodesComponent, 
    UndoDischargePatientComponent, 
    UploadAttachmentComponent, 
    SendNoteComponent, 
    AddMissingEncounterComponent, 
    SubmitMissingEncounterComponent, 
    EditEncounterSeenDateComponent, 
    RemoveReasonsCPTComponentComponent,
    CptCodeComponent,
    IcdCodeComponent,
    CptModifiersComponent,
    AlertComponent,
    DeleteEncounterComponent,
    EditDischargeDateComponent,
    SearchPatientsComponent,
    ActionsOfViewModifyEncounterComponent,
    EditSubgroupComponent
  ],
  exports: [
    ViewEncounterHistoryComponent, 
    EditPatientComponent, 
    ReplaceICDCodesComponent, 
    UndoDischargePatientComponent, 
    UploadAttachmentComponent, 
    SendNoteComponent, 
    AddMissingEncounterComponent, 
    EditEncounterSeenDateComponent, 
    RemoveReasonsCPTComponentComponent,
    CptCodeComponent,
    IcdCodeComponent,
    CptModifiersComponent,
    AlertComponent,
    DeleteEncounterComponent,
    EditDischargeDateComponent,
    SearchPatientsComponent,
    ActionsOfViewModifyEncounterComponent,
    EditSubgroupComponent
  ],
  imports: [
    CommonModule,
    CommonTaskRoutingModule,
    ChartModule,
    FormsModule,
    ReactiveFormsModule,
    NgxPaginationModule,
    NgMultiSelectDropDownModule.forRoot(),
    BsDatepickerModule.forRoot(),
    TooltipModule.forRoot(),
    MatInputModule,
    MtxNativeDatetimeModule,
    MtxDatetimepickerModule,
    SharedModule
  ],
  providers: [],
  bootstrap: [AddPatientComponent]
})
export class CommonTaskModule { }
