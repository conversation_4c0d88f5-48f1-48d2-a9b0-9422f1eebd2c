import { Component, OnInit, Input } from '@angular/core';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { CommonService } from 'src/app/services/common/common.service';
import { DatePipe } from '@angular/common';
import { ToastrService } from 'ngx-toastr';
declare let $:any;

@Component({
  selector: 'app-edit-subgroup',
  templateUrl: './edit-subgroup.component.html',
  styles: []
})
export class EditSubgroupComponent  implements OnInit {
  @Input() encounterObj:any;
  @Input() userType:string='';
  @Input() listofSubGroup:Array<any>=[];
  public request:any={};
  public form:FormGroup;
  public submitted:boolean=false;
  public subGroupName:string='';
  
  constructor(private readonly encrDecr: EncrDecrServiceService,private readonly fb: FormBuilder,
    private readonly commonServ:CommonService,public datepipe: DatePipe, private readonly toastr: ToastrService) { }

  ngOnInit() {
    this.form = this.fb.group({
      ddlSubgroup: [this.subGroupName, Validators.required]
    });
  }
  ngOnChanges(){
    if(this.encounterObj)
    {
      this.subGroupName=this.encounterObj.subGroupName;
    }
  }
  get f() { return this.form.controls };

  editSubgroup(eObj) {
    this.submitted = true;
    if (this.form.invalid) {
      return;
    }
    this.commonServ.startLoading();
    this.request.SubGroupName = this.encrDecr.set(this.form.value.ddlSubgroup);
    this.request.EncounterId = this.encrDecr.set(eObj.encounteR_ID);
    this.request.Type = this.encrDecr.set("UPDATE");
    this.commonServ.getUpdateSubgroups(this.request).subscribe((p: any) => {
      this.submitted = false;
        eObj.subGroupName = this.form.value.ddlSubgroup;
        this.request = {};
        $("#editSubgroupModal").modal('hide');
        this.toastr.success('Subgroup Updated Successfully!', '', { timeOut: 2500 });
      this.commonServ.stopLoading();
    }, error => {
      this.request = {};
      this.commonServ.stopLoading();
      console.error(error.status);
    });
  }

}
