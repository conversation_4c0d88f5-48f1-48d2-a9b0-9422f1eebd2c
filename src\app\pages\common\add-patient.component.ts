import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators, FormControl } from '@angular/forms';
import { AppComponent } from 'src/app/app.component';
import { FacilityModel } from 'src/app/models/facility.model';
import { PhysicianModel } from 'src/app/models/physician.model';
import { CommonService } from 'src/app/services/common/common.service';
import { CoordinatorService } from 'src/app/services/coordinator/coordinator.service';
import { ToastrService } from 'ngx-toastr';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { DatePipe } from '@angular/common';
import { MtxDatetimepickerType, MtxDatetimepickerMode, MtxCalendarView } from '@ng-matero/extensions/datetimepicker';
declare let $: any;

@Component({
  selector: 'app-add-patient',
  templateUrl: './add-patient.component.html',
  styleUrls: ['./add-patient.component.css']
})
export class AddPatientComponent implements OnInit {
  type: MtxDatetimepickerType = 'datetime';
  mode: MtxDatetimepickerMode = 'portrait';
  startView: MtxCalendarView = 'month';
  multiYearSelector = false;
  touchUi = false;
  twelvehour = false;
  timeInterval = 1;
  timeInput = true;
  public patientForm: FormGroup;
  public listOfFacilities = Array<FacilityModel>();
  public request: any = {};
  public submitted: boolean = false;
  public listOfPhysicians = Array<PhysicianModel>();
  public patient: any = {};
  public maxDate: any=this.datepipe.transform(new Date(), 'yyyy-MM-dd');

  constructor(private readonly appComp: AppComponent, private readonly fb: FormBuilder, private readonly commonServ: CommonService, private readonly coordinatorServ: CoordinatorService,
    private readonly encrDecr: EncrDecrServiceService, private readonly toastr: ToastrService, public datepipe: DatePipe) {
    this.commonServ.stopLoading();
  }

  ngOnInit() {
    this.appComp.loadPageName(history.state.title, history.state.tabName);
    this.getNonPrimeFacilities(history.state.userType);

    this.patientForm = this.fb.group({
      txtFirstName: ['', [Validators.required, Validators.maxLength(125)]],
      txtLastName: ['', [Validators.required, Validators.maxLength(125), UsernameValidatorAdd]],
      txtAccountNo: [''],
      txtMRN: ['', Validators.required],
      txtDOB: ['', Validators.required],
      ddlPatientClass: ['', Validators.required],
      ddlPhysician: ['', Validators.required],
      ddlFacilityName: ['', Validators.required],
      txtaddAdmissionDate: ['', Validators.required],
      txtDepartment: ['', Validators.required],
      txtRoomNo: ['', Validators.required],
      txtBedNo: ['', Validators.required],
      txtSSN: ['', [Validators.pattern(/^-?(0|[1-9]\d*)?$/), Validators.maxLength(9)]]
    });
  }

  get f() { return this.patientForm.controls; }

  getNonPrimeFacilities(userType) {
    this.coordinatorServ.getNonprimeFacilities(userType).subscribe((p: any) => {
      this.listOfFacilities = p;
    }, error => { console.error(error.status); });
  }

  getPhysiciansByFacility(facillity) {
    if (facillity == 'All') {
      facillity = '';
    }
    this.commonServ.startLoading();

    this.coordinatorServ.GetPhysiciansNonPrimeFacility(facillity).subscribe((p: any) => {
      this.commonServ.stopLoading();
      this.listOfPhysicians = p;
    }, error => {
      this.commonServ.stopLoading();
    });
    this.patientForm.value.ddlPhysician = "";
  }


  insertPatient() {
    this.submitted = true;
    if (this.patientForm.invalid) {
      return;
    }
    this.commonServ.startLoading();
    this.request.patient_name = this.encrDecr.set(this.patientForm.value.txtFirstName + " " + this.patientForm.value.txtLastName);
    this.request.account_number = this.encrDecr.set(this.patientForm.value.txtAccountNo);
    this.request.MRNumber = this.encrDecr.set(this.patientForm.value.txtMRN);
    this.request.dob = this.encrDecr.set(this.patientForm.value.txtDOB);
    this.request.departmentName = this.encrDecr.set(this.patientForm.value.txtDepartment);
    this.request.facility_name = this.encrDecr.set(this.patientForm.value.ddlFacilityName);
    this.request.physician_name = this.encrDecr.set(this.patientForm.value.ddlPhysician);
    this.request.patient_class = this.encrDecr.set(this.patientForm.value.ddlPatientClass);
    this.request.room_number = this.encrDecr.set(this.patientForm.value.txtRoomNo);
    this.request.bed_number = this.encrDecr.set(this.patientForm.value.txtBedNo);
    this.request.admit_datetime = this.encrDecr.set(this.datepipe.transform(this.patientForm.value.txtaddAdmissionDate, 'MM-dd-yyyy hh:mm:ss a'));
    this.request.ssn = this.encrDecr.set(this.patientForm.value.txtSSN);
    this.commonServ.insertPatient(this.request).subscribe((p: any) => {
      this.request = {};
      this.clearData();
      this.submitted = false;
      this.commonServ.stopLoading();
      this.toastr.success(p, '', { timeOut: 2500 });
    },
      error => {
        this.request = {};
        this.commonServ.stopLoading();
        console.error(error.status);
      });
  }

  clearData() {
    this.patientForm.get('txtFirstName')?.setValue('');
    this.patientForm.get('txtLastName')?.setValue('');
    this.patientForm.get('txtAccountNo')?.setValue('');
    this.patientForm.get('txtMRN')?.setValue('');
    this.patientForm.get('txtDOB')?.setValue('');
    this.patientForm.get('txtDepartment')?.setValue('');
    this.patientForm.get('ddlFacilityName')?.setValue('');
    this.patientForm.get('ddlPhysician')?.setValue('');
    this.patientForm.get('txtRoomNo')?.setValue('');
    this.patientForm.get('txtBedNo')?.setValue('');
    this.patientForm.get('txtaddAdmissionDate')?.setValue('');
    this.patientForm.get('ddlPatientClass')?.setValue('');
    this.patientForm.get('txtSSN')?.setValue('');
  }

}

export function UsernameValidatorAdd(control: FormControl) {
  let lastName = control.root.get('txtLastName');
  if (lastName) {
    let errorObj = { 'cannotContainSpace': false };
    let isEndDateValid = true;
    if (lastName.value) {
      if (lastName.value.indexOf(' ') >= 0) {
        isEndDateValid = false;
      }
    }
    lastName.setErrors((!isEndDateValid) ? errorObj : null);
    if (control.errors) { return { "cannotContainSpace": true } };
  }
  return null;
}
