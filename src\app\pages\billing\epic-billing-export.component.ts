import { Component, OnInit } from '@angular/core';
import { CommonService } from 'src/app/services/common/common.service';
import { ReportsService } from 'src/app/services/reports/reports.service';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { AppComponent } from 'src/app/app.component';
import { ExcelServices } from 'src/app/services/common/ExcelService';
import { DatePipe } from '@angular/common';
import { ToastrService } from 'ngx-toastr';
import { BillingService } from 'src/app/services/billing/billing.service';
declare let $: any;

@Component({
  selector: 'app-epic',
  templateUrl: './epic-billing-export.component.html',
  styleUrls: ['./epic-billing-export.component.css']
})
export class EpicBillingExportComponent implements OnInit {
  public listOfFacilities: Array<any> = [];
  public listOfReports: Array<any> = [];
  public FilterForm: FormGroup;
  public submitted: boolean = false;
  public request: any = {};
  public img1: string = '.././../../assets/img/excel.png';
  public mDdlGroupsSettings: any = {};
  public listOfGroups: Array<any> = [];
  public selectedListOfGroups: Array<any> = [];
  public totalCount: number=0;
  public totalCountToDownload: number=0;
  public p: number = 1;
  public mDdlPhysicianSettings: any = {};
  public listOfPhysicians: Array<any> = [];
  public selectedListOfPhysicians: Array<any> = [];
  constructor(private readonly billingServ: BillingService, private readonly reportsServ: ReportsService, private readonly commonServ: CommonService,
    private readonly appComp: AppComponent, private readonly fb: FormBuilder, private readonly excelService: ExcelServices, public datepipe: DatePipe,
    private readonly toastr: ToastrService, private readonly encrDecr: EncrDecrServiceService) {
    this.appComp.loadPageName('Epic Billing Export', 'billingTab');
  }

  ngOnInit() {
    this.getFacilities();
    this.FilterForm = this.fb.group({
      ddlFacility: ['', Validators.required],
      ddlGroups: ['',],
      ddlPhysician: [''],
      encounterSeenDateFrom: ['', [Validators.required]],
      encounterSeenDateTo: ['', [Validators.required]]
    });
    this.mDdlGroupsSettings = {
      singleSelection: false,
      idField: 'group_id',
      textField: 'group_name',
      enableCheckAll: true,
      itemsShowLimit: 1,
      allowSearchFilter: true,
      selectAllText: 'Select All',
      unSelectAllText: 'UnSelect All',
    };
    this.mDdlPhysicianSettings = {
      singleSelection: false,
      idField: 'physicianEmaiId',
      textField: 'physicianName',
      enableCheckAll: true,
      itemsShowLimit: 1,
      allowSearchFilter: true,
      selectAllText: 'Select All',
      unSelectAllText: 'UnSelect All',
    };
  }

  get f() { return this.FilterForm.controls; }

  getFacilities() {
    this.commonServ.startLoading();
    this.commonServ.getFacilitiesForEPIC().subscribe((p: any) => {
      this.commonServ.stopLoading();
      this.listOfFacilities = p;
    }, error => { console.error(error.status); });
  }

  getPhysiciansByFacility(facillity) {
    this.commonServ.startLoading();
    this.commonServ.getGroupNameByFacilityAndRole(facillity).subscribe((p: any) => {
      this.commonServ.stopLoading();
      this.selectedListOfPhysicians = [];
      this.selectedListOfGroups = [];
      this.selectedListOfGroups = [];
      this.listOfGroups = [];
      this.listOfGroups = p;
    }, error => {
      this.commonServ.stopLoading();
    });
  }

  onGroupSelect(item: any) {
    this.commonServ.startLoading();
    let group_names: string = '';
    this.selectedListOfGroups.forEach((grp: any) => {
      group_names = group_names + grp.group_name + ',';
    });
    this.reportsServ.getPhysiciansByGroup(group_names).subscribe((p: any) => {
      this.commonServ.stopLoading();
      this.selectedListOfPhysicians = [];
      this.listOfPhysicians = [];
      this.listOfPhysicians = p;
    }, error => {
      this.commonServ.stopLoading();
    });
  }

  onGroupSelectAll(item: any, type: string) {
    this.commonServ.startLoading();
    let group_names: string = '';
    if (type == 'S') {
      this.listOfGroups.forEach((grp: any) => {
        group_names = group_names + grp.group_name + ',';
      });
      this.reportsServ.getPhysiciansByGroup(group_names).subscribe((p: any) => {
        this.commonServ.stopLoading();
        this.selectedListOfPhysicians = [];
        this.listOfPhysicians = [];
        this.listOfPhysicians = p;
      }, error => {
        this.commonServ.stopLoading();
      });
    }
    else {
      this.commonServ.stopLoading();
      this.selectedListOfPhysicians = [];
      this.listOfPhysicians = [];
    }
  }

  filterReport(type: string) {
    this.submitted = true;
    if (this.FilterForm.invalid) {
      return;
    }
    let fromDate = new Date(this.FilterForm.value.encounterSeenDateFrom);
    let toDate = new Date(this.FilterForm.value.encounterSeenDateTo);
    let time = toDate.getTime() - fromDate.getTime();
    let days = time / (1000 * 3600 * 24);
    if (days >= 365) {
      this.toastr.error("Please select the Mark as Billed dates with in 1 year", '', { timeOut: 9000 });
      return;
    }
    if (fromDate > toDate) {
      this.toastr.error("From Date can't be greater than To Date", '', { timeOut: 9000 });
      return;
    }

    this.commonServ.startLoading();
    const group_ids = this.selectedListOfGroups.map(obj => obj.group_id).join(',');
    const phy_emails = this.selectedListOfPhysicians.map(obj => obj.physicianEmaiId).join(',');
    this.request.Facility_Name = this.encrDecr.set(this.FilterForm.value.ddlFacility);
    this.request.Group_Ids = this.encrDecr.set(group_ids);
    this.request.ProviderIds = this.encrDecr.set(phy_emails);
    this.request.ServiceFromDate = this.encrDecr.set(this.FilterForm.value.encounterSeenDateFrom);
    this.request.ServiceToDate = this.encrDecr.set(this.FilterForm.value.encounterSeenDateTo);
    this.request.Type = this.encrDecr.set(type);
    this.billingServ.getBillingDataToEpic(this.request).subscribe((p: any) => {
      this.totalCountToDownload = p.totalCount;
      this.p = 1;
      if (type == 'EXPORT') {
        this.exportAsXLSX(p);
      }
      else {
        this.listOfReports = p.exportToEpiclist;
        this.totalCount = p.exportToEpiclist.length;
      }
      this.request = {};
      this.commonServ.stopLoading();
    }, error => {
      this.commonServ.stopLoading();
    });
  }

  exportExcel() {
    let download = confirm("Are you sure you want to download the EPIC export file?.");
    if (download) {
      this.filterReport('EXPORT');
    }
  }

  exportAsXLSX(p: any): void {
    let fileName = p.batchNumber;
    let listOfData = p.exportToEpiclist;
    if (this.totalCount > 0 && fileName != '') {
      const headers = {
        recordIdentifier: 'Record Identifier',
        noColumn1: '',
        noColumn2: '',
        transactionType: 'Transaction Type',
        visitNumber: 'Visit Number',
        epicPatientMRN: 'Epic Patient MRN',
        patient: 'Patient',
        patientMRN: 'Patient MRN',
        patientMrnType: 'Patient MRN Type',
        accountType: 'Account Type',
        provider: 'Provider',
        billingProvider: 'Billing Provider',
        serviceDate: 'Service Date',
        placeOfService: 'Place of Service',
        placeOfServiceType: 'Place of Service Type',
        noColumn3: '',
        referralSource: 'Referral Source',
        department: 'Department',
        quantity: 'Quantity',
        diagnosis: 'Diagnosis',
        diagnosisCodeSet: 'Diagnosis Code Set',
        dxQualifier: 'Dx Qualifier',
        dxIndex: 'Dx Index',
        procedure: 'Procedure',
        modifier: 'Modifier',
        admissionDate: 'Admission Date',
        dischargeDate: 'Discharge Date'
      };
      const list = listOfData.map(item => {
        return {
          recordIdentifier: item.recordIdentifier,
          noColumn1: item.noColumn1,
          noColumn2: item.noColumn2,
          transactionType: item.transactionType,
          visitNumber: item.visitNumber,
          epicPatientMRN: '',
          patient: item.patient,
          patientMRN: item.patientMRN,
          patientMrnType: item.patientMrnType,
          accountType: item.accountType,
          provider: item.provider,
          billingProvider: item.billingProvider,
          serviceDate: item.serviceDate,
          placeOfService: item.placeOfService,
          placeOfServiceType: item.placeOfServiceType,
          noColumn3: item.noColumn3,
          referralSource: item.referralSource,
          department: item.department,
          quantity: item.quantity,
          diagnosis: item.diagnosis,
          diagnosisCodeSet: item.diagnosisCodeSet,
          dxQualifier: item.dxQualifier,
          dxIndex: item.dxIndex,
          procedure: item.procedure,
          modifier: item.modifier,
          admissionDate: item.admissionDate,
          dischargeDate: item.dischargeDate
        }
      });
      let batchItem = {
        recordIdentifier: 'BATCH',
        noColumn1: '300',
        noColumn2: 'IMPORT',
        transactionType: '',
        visitNumber: '',
        epicPatientMRN: '',
        patient: '',
        patientMRN: '',
        patientMrnType: '',
        accountType: '',
        provider: '',
        billingProvider: '',
        serviceDate: '',
        placeOfService: '',
        placeOfServiceType: '',
        noColumn3: '',
        referralSource: '',
        department: '',
        quantity: '',
        diagnosis: '',
        diagnosisCodeSet: '',
        dxQualifier: '',
        dxIndex: '',
        procedure: '',
        modifier: '',
        admissionDate: '',
        dischargeDate: ''
      }
      list.unshift(batchItem);

      this.excelService.exportAsExcelEpicData(list, headers, fileName);
      setTimeout(() => {
        this.filterReport('VIEW');
      }, 1000);
    }
    else {
      this.toastr.error('No data found to download the file.', '', { timeOut: 9000 });
    }
  }

}
