<!-- Page Content Begin -->
<div class="container-fluid">
    <div class="row">
        <div class="col px-0">
            <div class="card shadow border-blue">
                <!-- Card Header - Dropdown -->
                <div class="card-header-new px-0 py-2" style="background: #0169ab;">
                    <div class="col-12 position-relative vertical-align-middle">
                        <div class="mx-auto row">
                            <div [ngClass]="facilityType=='1'?'col-5 col-md-1 p-1':'col-6 col-md-1 p-1'">
                                <button class="btn btn-secondary btn-block" (click)="back()">Back</button>
                            </div>
                            <ng-container *ngIf="encounterId; else apprvElse">
                                <div class="col-6 col-md-2 p-1">
                                    <button *ngIf="userAccess.residentAccess=='NO'; else btnAElse"
                                        class="btn btn-info btn-block" (click)="approveAndEdit()">Approve</button>
                                    <ng-template #btnAElse>
                                        <button class="btn btn-info btn-block" (click)="updateChanges()">Update
                                            Changes</button>
                                    </ng-template>
                                </div>
                                <div class="col-6 col-md-2 p-1">
                                    <button class="btn btn-info btn-block btn-danger"
                                        (click)="deleteEncounter()">Delete</button>
                                </div>
                            </ng-container>
                            <ng-template #apprvElse>
                                <div class="col-6 col-md-3 p-1"
                                    *ngIf="facilityType=='1' && userAccess.residentAccess=='NO'; else elResDate">

                                    <mat-form-field>
                                        <mtx-datetimepicker #datetimePicker1 [type]="type" [mode]="mode"
                                            [multiYearSelector]="multiYearSelector" [startView]="startView"
                                            [twelvehour]="twelvehour" [timeInterval]="timeInterval" [touchUi]="touchUi"
                                            [timeInput]="timeInput">
                                        </mtx-datetimepicker>
                                        <input [mtxDatetimepicker]="datetimePicker1" [max]="maxDateTime" class="form-row"
                                            [(ngModel)]="testData.encounterSeenDate" matInput required>
                                        <mtx-datetimepicker-toggle [for]="datetimePicker1"
                                            matSuffix></mtx-datetimepicker-toggle>
                                    </mat-form-field>
                                </div>
                                <ng-template #elResDate>
                                    <div class="col-6 col-md-3 p-1" *ngIf="userAccess.residentAccess=='YES'">

                                        <mat-form-field>
                                            <mtx-datetimepicker #datetimePicker2 [type]="type" [mode]="mode"
                                                [multiYearSelector]="multiYearSelector" [startView]="startView"
                                                [twelvehour]="twelvehour" [timeInterval]="timeInterval"
                                                [touchUi]="touchUi" [timeInput]="timeInput">
                                            </mtx-datetimepicker>
                                            <input [mtxDatetimepicker]="datetimePicker2" [max]="maxDateTime" class="form-row"
                                                [(ngModel)]="testData.encounterSeenDate" matInput required>
                                            <mtx-datetimepicker-toggle [for]="datetimePicker2"
                                                matSuffix></mtx-datetimepicker-toggle>
                                        </mat-form-field>
                                    </div>
                                </ng-template>

                                <div class="col-6 col-md-2 p-1" *ngIf="userAccess.residentAccess=='YES'">
                                    <select class="form-control custom-control" [(ngModel)]="selectedProvider"
                                        [ngModelOptions]="{standalone: true}">
                                        <option value='null'>---Select Physician---</option>
                                        <option [value]="s.attending_provider" *ngFor="let s of listOfProvider">
                                            {{s.attending_provider}}
                                        </option>
                                    </select>
                                </div>

                                <div class="col-6 col-md-2 p-1"
                                    *ngIf="facilityType=='0' && userAccess.residentAccess=='NO'; else singSel">
                                    <button class="btn btn-info btn-block" (click)="openMultiSelectPop()">Mark As
                                        Seen</button>
                                </div>
                                <ng-template #singSel>
                                    <div class="col-6 col-md-2 p-1">
                                        <button class="btn btn-info btn-block" (click)="starNewEncounter(0)"><span
                                                *ngIf="userAccess.residentAccess=='YES';else resEls">Submit for
                                                approval</span>
                                            <ng-template #resEls>Mark As Seen</ng-template>
                                        </button>
                                    </div>
                                </ng-template>

                            </ng-template>

                            <div class="col-6 col-md-2 p-1">
                                <button class="btn btn-outline-info btn-block" (click)="viewHistory(patient)">View
                                    History</button>
                            </div>
                            <div class="col-6 col-md-2 p-1">
                                <button class="btn btn-outline-info btn-block"
                                    (click)="openNotes(patient)">Notes({{testData.notesCount}})</button>
                            </div>
                            <div class="col-6 col-md-2 p-1">
                                <button class="btn btn-outline-info btn-block" [title]="testData.attachementLastUploadedDate ? 'Last Uploaded on ' + testData.attachementLastUploadedDate : ''"
                                    (click)="getAttachments(patient)">Attachments({{testData.attachementCount}})
                                </button>
                            </div>
                        </div>
                    </div>
                </div>


                <div class="card-body px-2 py-1">
                    <!-- table Card starts -->
                    <div *ngIf="!device" class="row mx-0 my-1">
                        <div class="col-12 px-0">
                            <!-- table start -->
                            <div>
                                <table class="table mb-0 customDataTable width-100per" border="1">
                                    <thead>
                                        <tr>
                                            <th class="text-center pb-0">Patient Name</th>
                                            <th class="text-center pb-0">Account No#</th>
                                            <th class="text-center pb-0">Facility</th>
                                            <th class="text-center pb-0">Age</th>
                                            <th class="text-center pb-0">Room No</th>
                                            <th class="text-center pb-0">Patient Type</th>
                                            <th class="text-center pb-0">Attending Provider</th>
                                            <th class="text-center pb-0">LOS</th>
                                            <th class="text-center pb-0">Payer Class</th>
                                            <th class="text-center pb-0">Admission Date</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td class="text-left text-md-center pb-0" data-th="Patient Name"><span
                                                    class="mobile-text">{{patient.patient_Name}}</span></td>
                                            <td class="text-left text-md-center pb-0" data-th="Account No#"><span
                                                    class="mobile-text">{{patient.account_Number}}</span></td>
                                            <td class="text-left text-md-center pb-0" data-th="Facility"><span
                                                    class="mobile-text">{{patient.facility_Name}}</span></td>
                                            <td class="text-left text-md-center pb-0" data-th="Age"><span
                                                    class="mobile-text">{{patient.age}}</span></td>
                                            <td class="text-left text-md-center pb-0" data-th="Room No"><span
                                                    class="mobile-text">{{patient.room_Number}}</span></td>
                                            <td class="text-left text-md-center pb-0" data-th="Patient Type"><span
                                                    class="mobile-text">{{patient.admission_Type}}</span></td>
                                            <td class="text-left text-md-center pb-0" data-th="Attending Provider"><span
                                                    class="mobile-text">{{patient.attending_Physician_InApp}}</span>
                                            </td>
                                            <td class="text-left text-md-center pb-0" data-th="LOS"><span
                                                    class="mobile-text">{{patient.arithmetic_Mean_LOS}}</span></td>
                                            <td class="text-left text-md-center pb-0" data-th="Payer Class"><span
                                                    class="mobile-text">{{patient.reimbursement_Type}}</span></td>
                                            <td class="text-left text-md-center pb-0" data-th="Admission Date"><span
                                                    class="mobile-text">{{patient.admit_Datetime | date:
                                                    'MM/dd/yyyy'}}</span></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <!-- table end -->
                        </div>
                        <div class="col-3 p-1">
                            
                        </div>
                    </div>
                    <!-- table card ends-->
                    <div *ngIf="device" class="row mx-0 text-secondary border bg-light my-2">
                        <div class="col-12 m-auto p-1">
                            <div class="d-inline-block m-marbtn pr-md-2 pr-2 text-left m-auto callout callout-primary bg-white">
                                <h6 class="card-title m-big my-auto px-1" style="font-size: 1rem !important;">Name-: <span>{{patient.patient_Name}}</span>
                                </h6>
                            </div>
                            <div class="d-inline-block m-marbtn pr-md-2 pr-2 text-left callout callout-primary bg-white">
                                <h6 class="card-title m-big my-auto px-1" style="font-size: 1rem !important;">Account:
                                    <span>{{patient.account_Number}}</span></h6>
                            </div>
                            <div class="d-inline-block m-marbtn pr-md-2 pr-2 text-left callout callout-primary bg-white">
                                <h6 class="card-title m-big my-auto px-1" style="font-size: 1rem !important;">Facility:
                                    <span>{{patient.facility_Name}}</span></h6>
                            </div>
                            <div class="d-inline-block m-marbtn pr-md-2 pr-2 text-left callout callout-primary bg-white">
                                <h6 class="card-title m-big my-auto px-1" style="font-size: 1rem !important;">Room: <span>{{patient.room_Number}}</span>
                                </h6>
                            </div>
                            <div class="d-inline-block m-marbtn pr-md-2 pr-2 text-left callout callout-primary bg-white">
                                <h6 class="card-title m-big my-auto px-1" style="font-size: 1rem !important;">Age: <span>{{patient.age}}</span></h6>
                            </div>
                            <div class="d-inline-block m-marbtn pr-md-2 text-left callout callout-primary bg-white">
                                <h6 class="card-title m-big my-auto px-1" style="font-size: 1rem !important;">Payer Class:
                                    <span>{{patient.reimbursement_Type}}</span></h6>
                            </div>
                            <div class="d-inline-block m-marbtn pr-md-2 pr-2 text-left callout callout-primary bg-white">
                                <h6 class="card-title m-big my-auto px-1" style="font-size: 1rem !important;">Patient Type:
                                    <span>{{patient.admission_Type}}</span></h6>
                            </div>
                            <div class="d-inline-block m-marbtn pr-md-2 text-left callout callout-primary bg-white">
                                <h6 class="card-title m-big my-auto px-1" style="font-size: 1rem !important;">Admission Date: <span>{{patient.admit_Datetime
                                        | date: 'MM/dd/yyyy'}}</span></h6>
                            </div>
                            <div class="d-inline-block m-marbtn pr-md-2 text-left callout callout-primary bg-white">
                                <h6 class="card-title m-big my-auto px-1" style="font-size: 1rem !important;">LOS:
                                    <span>{{patient.arithmetic_Mean_LOS}}</span></h6>
                            </div>
                            <div class="d-inline-block m-marbtn pr-md-2 text-left callout callout-primary bg-white">
                                <h6 class="card-title m-big my-auto px-1" style="font-size: 1rem !important;">Attending Provider:
                                    <span>{{patient.attending_Physician_InApp}}</span></h6>
                            </div>
                        </div>
                    </div>
                    <!-- sub groups card starts-->
                    <div class="row mx-0 mt-2" *ngIf="testData.listofSubGroup?.length>0 && userAccess.residentAccess=='NO'">
                        <div class="col-12 col-md-12 px-0">
                            <div class="mb-2">
                                <div class="card">
                                    <div class="card-header-new py-0 px-0 dan-blue">
                                        <h5 class="mb-0 position-relative">
                                            <button class="btn btn-link collapsed align-items-center d-flex text-white">
                                                <span>Subgroups</span>
                                                <span class="ml-3">
                                                    <img alt="" src="../../../assets/info-icon.svg" width="15" />
                                                    <span class="small text-light aria-label m-1"> Defaults to {{testData.group_name}} group if no subgroup is selected</span>
                                                </span>
                                            </button>
                                        </h5>
                                    </div>
                    
                                    <div class="collapse show">
                                        <div class="card-body" id="ddlSubGrups">
                                            <div class="row">
                                                <div class="col-12 col-md-3">
                                                    <select class="form-control custom-control border-blue" [(ngModel)]="sub_group_name"
                                                        [ngModelOptions]="{standalone: true}">
                                                        <option value=''>---Select Subgroup---</option>
                                                        <option [value]="s.subGroupName" *ngFor="let s of testData.listofSubGroup">
                                                            {{s.subGroupName}}
                                                        </option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- sub groups card ends-->
                    <!-- icd cpt card starts-->
                    <div class="row mx-0 mt-2">
                        <div class="col-12 px-0">
                            <!-- inner accordion 1-->
                            <div class="mb-2">
                                <div class="card">
                                    <div class="card-header-new py-0 px-0 dan-blue">
                                        <h5 class="mb-0 position-relative">
                                            <button class="btn btn-link collapsed align-items-center d-flex text-white">
                                                <span>CPT/HCPCS Codes</span>
                                            </button>
                                            <a (click)="getCPTData()"
                                                class="float-right position-absolute top-right-10 blld"
                                                data-toggle="modal" data-target="#CPTDataPhy"><i
                                                    class="fas fa-plus-circle fa-2x "></i></a>
                                        </h5>
                                    </div>

                                    <div class="collapse show">
                                        <div class="card-body" id="cptDiv">
                                            <div class="row" *ngFor="let cpt of testData.listofTestCPTS">
                                                <div>
                                                    <span class="mx-1 edit-ancor"
                                                        (keyup)="deleteCPTCode(testData.listofTestCPTS,cpt)"
                                                        (click)="deleteCPTCode(testData.listofTestCPTS,cpt)">
                                                        <i class="far fa-trash-alt" title="Remove"></i>
                                                    </span>
                                                </div>
                                                <div class="col-10">
                                                    <span class="cpt-colors">{{cpt}}</span>
                                                </div>
                                                <div>
                                                    <a href="#" class="mx-1" data-toggle="modal"
                                                        data-target="#ModifierDataPhy" (click)="getModifierData(cpt)">
                                                        Add/Modify Modifiers</a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- inner accordion end 1-->
                            <!-- inner accordion 2-->
                            <div class="mb-2">
                                <div class="card">
                                    <div class="card-header-new py-0 px-0">
                                        <h5 class="mb-0 position-relative dan-blue">
                                            <button
                                                class="btn btn-link collapsed align-items-center d-flex text-white"><span>ICD
                                                    Codes</span></button>
                                            <a (click)="getICDData()"
                                                class="float-right position-absolute top-right-10 blld"
                                                data-toggle="modal" data-target="#ICDDataPhy"><i
                                                    class="fas fa-plus-circle fa-2x "></i></a>
                                        </h5>
                                    </div>

                                    <div class="collapse show">
                                        <div class="card-body">
                                            <div class="row" *ngFor="let icd of testData.listofTestICDS">
                                                <div class="blld">
                                                    <span class="mx-1 edit-ancor"
                                                        (keyup)="deleteICDCode(testData.listofTestICDS,icd)"
                                                        (click)="deleteICDCode(testData.listofTestICDS,icd)">
                                                        <i class="far fa-trash-alt" title="Remove"></i>
                                                    </span>
                                                </div>
                                                <div class="col-8">
                                                    <span class="cpt-colors">{{icd}}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                            <!-- inner accordion end 2-->
                        </div>
                    </div>
                    <!-- icd cpt card ends-->

                </div>



            </div>
        </div>
    </div>
</div>
<!-- Page Content Ends -->

<!-- Confirm Message Modal Start -->
<div class="modal fade" id="markAsSeenConfModel" tabindex="-1" aria-labelledby="markAsSeenConfModelLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable modal-lg">
        <div class="modal-content">
            <div class="modal-header py-2">
                <h5 class="modal-title h5" id="markAsSeenConfModelLabel">Confirm Message</h5>
                <i class="fa fa-times closePopup" data-dismiss="modal"></i>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <span *ngIf="isMultipleEncounter">Selected encounter seen date: <span
                            class='font-weight-bold'>{{encounterSeenDates}}</span></span>
                    <span *ngIf="isMultipleEncounter">Same CPT and ICD codes will be applied for all the selected
                        encounters</span>
                    <div class="text-center"><span class="font-weight-bold">Are you sure want to proceed?</span></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary btn-sm" data-dismiss="modal">No</button>
                <button type="button" class="btn btn-outline-info btn-sm"
                    (click)="confirmStarNewEncounter(encounterStatus)">Yes</button>
            </div>
        </div>
    </div>
</div>
<!-- Confirm Message End -->

<!-- Multiselect calender Modal Start -->
<div class="modal fade" id="mulitpleDatesModel" tabindex="-1" aria-labelledby="multipleDatesModelLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable modal-lg">
        <div class="modal-content">
            <div class="modal-header py-2">
                <h5 class="modal-title h5" id="multipleDatesModelLabel">Encounter Seen Date</h5>
                <i class="fa fa-times closePopup" data-dismiss="modal"></i>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <div class="row">
                        <div class="col-2">
                            <label class="switch mt-2"><input type="checkbox" [(ngModel)]="isMultipleEncounter"
                                    class="success"><span class="slider round"></span></label>
                        </div>
                        <div class="col-8" *ngIf="isMultipleEncounter; else calElse">
                            <mat-form-field class="w-100">
                                <ngx-multiple-dates [matDatepicker]="picker" name="excludedDates"
                                    [(ngModel)]="listOfEncounterSeenDates" [min]='minDate' [max]='maxDate'>
                                </ngx-multiple-dates>
                                <mat-datepicker-toggle matPrefix [for]="picker"></mat-datepicker-toggle>
                                <mat-datepicker #picker></mat-datepicker>
                            </mat-form-field>
                        </div>
                        <ng-template #calElse>
                            <div class="col-8">
                                <mat-form-field>
                                    <mtx-datetimepicker #datetimePicker3 [type]="type" [mode]="mode"
                                        [multiYearSelector]="multiYearSelector" [startView]="startView"
                                        [twelvehour]="twelvehour" [timeInterval]="timeInterval" [touchUi]="touchUi"
                                        [timeInput]="timeInput">
                                    </mtx-datetimepicker>
                                    <input [mtxDatetimepicker]="datetimePicker3" [max]="maxDateTime"
                                        [(ngModel)]="testData.encounterSeenDate" matInput required>
                                    <mtx-datetimepicker-toggle [for]="datetimePicker3"
                                        matSuffix></mtx-datetimepicker-toggle>
                                </mat-form-field>
                            </div>
                        </ng-template>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-info" (click)="selectMultiSelectDate()">Ok</button>
            </div>
        </div>
    </div>
</div>
<!-- Multiselect calender End -->

<!-- CPT Codes popup starts -->
<app-cpt-data [lisfOfCPTData]="lisfOfCPTData"></app-cpt-data>
<!-- CPT Codes popup ends -->

<!-- ICD Codes popup starts -->
<app-icd-data [lisfOfICDData]="lisfOfICDData"></app-icd-data>
<!-- ICD Codes popup end -->

<!-- Cpt Modifier popup starts -->
<app-cptmodifiers [listOfModifier]="listOfModifier" [cptCode]="cptCode"></app-cptmodifiers>
<!-- Cpt Modifier popup end -->

<!-- send note Popup Starts -->
<app-send-note [PatientObject]='patient' [listOfUsersAndGroups]="listOfUsersAndGroups" [lisfOfSentNotes]="lisfOfSentNotes"
    [selectedUsersAndGroups]="selectedUsersAndGroups" (eventListOfNotes)="openNotes(patient)"></app-send-note>
<!-- send note Popup Ends -->

<!-- attachment popup starts -->
<app-upload-attachment (eventUpdateCount)="updateAttCount(patient)" [lisfOfAttachments]="lisfOfAttachments"
    [PatientObject]="patient" [userType]="'PHYSICIAN'"></app-upload-attachment>
<!-- attachment popup ends -->

<!---- App View History Start-->
<app-view-history [listOfPatientHistory]='listOfPatientHistory' [patient]='patient'
    [historyTotalCount]="historyTotalCount"></app-view-history>
<!---- App View History End-->