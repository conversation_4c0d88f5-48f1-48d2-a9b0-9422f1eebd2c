import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { apiUrl } from 'src/app/config';
import { EncrDecrServiceService } from '../common/encr-decr-service.service';

@Injectable({
  providedIn: 'root'
})
export class PhysicianService {

  constructor(private readonly http: HttpClient, private readonly encrDecr: EncrDecrServiceService) { }

  getEncounterTestDetails(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Physician/GetEncounterTestDetails', request, { headers: headers });
  }

  getModifiers(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Billing/GetModifiers', request, { headers: headers });
  }

  GetMyPatientsDetails(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Physician/GetMyPatientsDetails', request, { headers: headers });
  }

  startNewEncounter(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Physician/StartNewEncounter', request, { headers: headers });
  }

  submitPhysicianMultipleEncounters(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Physician/SubmitPhysicianMultipleEncounters', request, { headers: headers });
  }

  RemovePatient(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Physician/RemovePatient', request, { headers: headers });
  }

  HideOrUnHidePatient(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Physician/HideOrUnHidePatient', request, { headers: headers });
  }

  AssignToothersforPhyList(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Physician/AssignToothersforPhyList', request, { headers: headers });
  }

  assignToMyList(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Physician/AssignToMyList', request, { headers: headers });
  }
  AssignToResidentsforPhy(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Physician/AssignToResidentsforPhy', request, { headers: headers });
  }

  getProvidersForResident() {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.get(apiUrl + 'api/Physician/GetProvidersForResident', { headers: headers });
  }

  GetResidents(facillity) {
    const body = { ParamOne: this.encrDecr.set(facillity) };
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Physician/GetResidents', body, { headers: headers });
  }

  getPatientHistory(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Physician/GetPatientHistory', request, { headers: headers });
  }

  DownLoadPateintList(IsHide) {
    const body = { ParamOne: this.encrDecr.set(IsHide) };
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Physician/DownLoadPateintList', body, { headers: headers });
  }

  CloseAndPostBillerStatus(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Physician/CloseAndPostBillerStatus', request, { headers: headers });
  }

  getUnBilledEncounters(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Physician/GetUnBilledEncounters',request, { headers: headers });
  }

  getHospitalCensusData(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Physician/GetHospitalCensusData', request, { headers: headers });
  }

  GetMyGroupPatientsDetails(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Physician/GetMyGroupPatientsDetails', request, { headers: headers });
  }

  RemoveGroupForPatient(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Physician/RemoveGroupForPatient', request, { headers: headers });
  }

  getPendingApprovalData(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Physician/GetPendingApprovalData',request, { headers: headers });
  }

  getDischargePatientData(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Physician/GetDischargePatients', request, { headers: headers });
  }
  getPatientsDataForPhysician(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Physician/GetPatientsDataForPhysician', request, { headers: headers });
  }

  getEncountersByPatient(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Physician/AsyncGetEncountersByPatientForPhysician', request, { headers: headers });
  }

  getPendingEncounters(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Physician/GetPendingEncounters', request, { headers: headers });
  }

  approveorDeleteEncounter(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Physician/ApproveorDeleteEncounter', request, { headers: headers });
  }

  approvePendingEncounters(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Physician/ApprovePendingEncounters', request, { headers: headers });
  }

  approveEditEncounterByPhysician(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Physician/ApproveEditEncounterByPhysician', request, { headers: headers });
  }

  residentEditCharges(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Physician/ResidentEditCharges', request, { headers: headers });
  }


}
