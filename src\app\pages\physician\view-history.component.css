/* Mobile Responsive Styles for View History Component */

/* Desktop styles - default table layout */
.patient-history-table {
    width: 100%;
}

.patient-history-table th,
.patient-history-table td {
    text-align: center;
    padding: 0.5rem;
}

/* Mobile-specific styles for merged patient information display */
@media (max-width: 767px) {
    /* Hide the original table headers and restructure for mobile */
    .patient-history-table thead {
        display: none;
    }

    .patient-history-table tbody {
        display: block;
    }

    .patient-history-table tbody tr {
        display: block;
        margin-bottom: 1rem;
        border: 1px solid #e3e6f0;
        border-radius: 8px;
        padding: 0.75rem;
        background-color: #fff;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        position: relative;
    }

    .patient-history-table tbody td {
        display: block;
        text-align: left;
        padding: 0.25rem 0;
        border: none;
    }

    /* Hide desktop table cells on mobile */
    .patient-history-table tbody td:nth-child(2),
    .patient-history-table tbody td:nth-child(3),
    .patient-history-table tbody td:nth-child(4),
    .patient-history-table tbody td:nth-child(5) {
        display: none !important;
    }

    /* Style the View button cell */
    .patient-history-table tbody td:first-child {
        margin-bottom: 0.5rem;
        text-align: center;
        display: block !important;
    }

    /* Create merged patient info display */
    .mobile-patient-info {
        display: block !important;
        padding: 0.5rem 0;
        line-height: 1.4;
        font-size: 0.95rem;
        border: none !important;
        text-align: left !important;
    }

    .mobile-patient-info .patient-name {
        display: block;
        font-weight: 600;
        color: #336699;
        margin-bottom: 0.25rem;
        font-size: 1rem;
    }

    .mobile-patient-info .account-number {
        display: inline;
        color: #666;
        /*margin-right: 0.5rem;*/
        font-size: 0.9rem;
    }

    .mobile-patient-info .provider-name {
        display: inline;
        font-weight: bold;
        color: #333;
        font-size: 0.9rem;
    }

    .mobile-patient-info .separator {
        color: #999;
        margin: 0 0.25rem;
        font-weight: normal;
    }

    .mobile-patient-info .patient-details {
        margin-bottom: 0.25rem;
    }

    .mobile-patient-info .encounter-date {
        display: block;
        color: #666;
        font-size: 0.85rem;
        font-style: italic;
        margin-top: 0.25rem;
    }
    
    /* Enhanced button styling for mobile */
    .patient-history-table .btn-outline-info {
        font-size: 0.875rem;
        padding: 0.375rem 0.75rem;
        border-radius: 6px;
        min-width: 80px;
    }
    
    /* Load More button styling for mobile */
    .load-more-mobile {
        text-align: center;
        padding: 1rem;
        border: 1px solid #e3e6f0;
        border-radius: 8px;
        background-color: #f8f9fc;
        margin-top: 0.5rem;
    }
    
    .load-more-mobile .btn-info {
        font-size: 0.875rem;
        padding: 0.5rem 1rem;
        border-radius: 6px;
    }
}

/* iPhone specific optimizations */
@media (max-width: 767px) and (-webkit-min-device-pixel-ratio: 2) {
    .mobile-patient-info {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
    
    .patient-history-table .btn-outline-info {
        -webkit-appearance: none;
        -webkit-tap-highlight-color: transparent;
    }
}

/* Android specific optimizations */
@media (max-width: 767px) and (min-resolution: 192dpi) {
    .mobile-patient-info .patient-name {
        font-weight: 700; /* Slightly bolder for Android */
    }
    
    .mobile-patient-info .provider-name {
        font-weight: 800; /* Extra bold for Android */
    }
}

/* Very small screens (iPhone SE, etc.) */
@media (max-width: 375px) {
    .patient-history-table tbody tr {
        padding: 0.5rem;
    }
    
    .mobile-patient-info {
        font-size: 0.9rem;
    }
    
    .patient-history-table .btn-outline-info {
        font-size: 0.8rem;
        padding: 0.3rem 0.6rem;
        min-width: 70px;
    }
}

/* Landscape orientation adjustments for mobile */
@media (max-width: 767px) and (orientation: landscape) {
    .patient-history-table tbody tr {
        margin-bottom: 0.75rem;
        padding: 0.6rem;
    }
    
    .mobile-patient-info {
        font-size: 0.9rem;
    }
}
